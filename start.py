#!/usr/bin/env python3
"""
Pokemon TCG Scraper - Main Launcher
Simple launcher like on Mac - no virtual environment issues
"""

import os
import sys
import subprocess

def main():
    print("🎯 Pokemon TCG Scraper - Main Launcher")
    print("=" * 50)
    
    # Project paths
    project_root = "/media/devmon/piHDD/Sites/pokemon-tcg-scraper"
    launcher_file = os.path.join(project_root, "launcher.py")
    
    print(f"📂 Project root: {project_root}")
    
    # Check if files exist
    if not os.path.exists(project_root):
        print(f"❌ Project directory not found: {project_root}")
        return
        
    if not os.path.exists(launcher_file):
        print(f"❌ Launcher file not found: {launcher_file}")
        return
    
    print("✅ All files found")
    print()
    
    # Prepare environment
    env = os.environ.copy()
    env['PYTHONPATH'] = project_root
    
    # Remove virtual environment if present
    if 'VIRTUAL_ENV' in env:
        del env['VIRTUAL_ENV']
        print("🔧 Virtual environment disabled")
    
    print("🚀 Starting Pokemon TCG Scraper...")
    print("📍 Interactive menu loading...")
    print("⚡ Like on your Mac!")
    print("=" * 50)
    print()
    
    try:
        # Launch the main application
        subprocess.run([
            'python3', 'launcher.py'
        ], cwd=project_root, env=env)
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
