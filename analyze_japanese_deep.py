#!/usr/bin/env python3
"""
Analyse approfondie du site japonais avec simulation de navigateur
pour capturer le contenu JavaScript/AJAX
"""

import requests
from bs4 import BeautifulSoup
import re
import time
import json

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
    'Referer': 'https://www.pokemon-card.com/',
    'Connection': 'keep-alive'
}

def analyze_search_results_structure():
    """Analyser la structure des résultats de recherche"""
    print("🔍 ANALYSE RÉSULTATS DE RECHERCHE")
    print("=" * 50)
    
    # Essayer différentes approches de recherche
    search_urls = [
        "https://www.pokemon-card.com/card-search/index.php?keyword=&se_ta=&regulation_sidebar_form=all&pg=1",
        "https://www.pokemon-card.com/card-search/index.php?pg=1",
        "https://www.pokemon-card.com/card-search/",
    ]
    
    for i, url in enumerate(search_urls, 1):
        print(f"\n🧪 TEST {i}: {url}")
        
        try:
            response = requests.get(url, headers=HEADERS, timeout=15)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Chercher des patterns de cartes
                card_patterns = [
                    soup.find_all('div', class_=re.compile(r'card', re.I)),
                    soup.find_all('li', class_=re.compile(r'card|item', re.I)),
                    soup.find_all('article'),
                    soup.find_all('div', {'data-card': True}),
                    soup.find_all('a', href=re.compile(r'detail|card'))
                ]
                
                for j, pattern in enumerate(card_patterns):
                    if pattern:
                        print(f"  Pattern {j+1}: {len(pattern)} éléments trouvés")
                        
                        # Analyser le premier élément
                        if pattern:
                            first = pattern[0]
                            print(f"    Classes: {first.get('class', [])}")
                            print(f"    Attributs: {list(first.attrs.keys())}")
                            
                            # Chercher des liens
                            links = first.find_all('a', href=True)
                            for link in links[:2]:
                                href = link.get('href', '')
                                text = link.get_text(strip=True)
                                print(f"    Lien: {href} - {text}")
                
                # Chercher des scripts qui pourraient contenir des données
                scripts = soup.find_all('script')
                for script in scripts:
                    content = script.get_text()
                    if any(keyword in content for keyword in ['card', 'search', 'data', 'ajax']):
                        # Extraire des patterns intéressants
                        urls = re.findall(r'https?://[^\s"\']+', content)
                        for url in urls[:3]:
                            if 'card' in url or 'search' in url:
                                print(f"    Script URL: {url}")
                
                return True
            else:
                print(f"❌ Erreur: {response.status_code}")
        
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    return False

def find_api_endpoints():
    """Chercher des endpoints API ou AJAX"""
    print("\n🔍 RECHERCHE D'ENDPOINTS API")
    print("=" * 50)
    
    base_url = "https://www.pokemon-card.com"
    
    # URLs potentielles à tester
    potential_apis = [
        "/card-search/api/",
        "/api/card-search/",
        "/card-search/search.php",
        "/card-search/list.php",
        "/card-search/ajax/",
        "/ajax/card-search/",
        "/search/",
        "/api/cards/",
    ]
    
    for api_path in potential_apis:
        url = base_url + api_path
        print(f"\n🧪 Test: {url}")
        
        try:
            response = requests.get(url, headers=HEADERS, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                print(f"Content-Type: {content_type}")
                
                if 'json' in content_type:
                    try:
                        data = response.json()
                        print(f"JSON Response: {type(data)} avec {len(data) if isinstance(data, (list, dict)) else 'N/A'} éléments")
                    except:
                        print("Réponse JSON invalide")
                else:
                    content = response.text[:200]
                    print(f"Contenu: {content}...")
            
        except Exception as e:
            print(f"Erreur: {e}")

def analyze_card_detail_patterns():
    """Analyser les patterns des pages de détail"""
    print("\n🔍 ANALYSE PATTERNS DÉTAIL CARTES")
    print("=" * 50)
    
    # Essayer des patterns d'URLs de détail courants
    detail_patterns = [
        "https://www.pokemon-card.com/card-search/detail.php?id=1",
        "https://www.pokemon-card.com/card-search/detail/1",
        "https://www.pokemon-card.com/card/detail/1",
        "https://www.pokemon-card.com/cards/1",
    ]
    
    for pattern in detail_patterns:
        print(f"\n🧪 Test pattern: {pattern}")
        
        try:
            response = requests.get(pattern, headers=HEADERS, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Chercher des éléments caractéristiques d'une carte
                card_indicators = [
                    soup.find_all('img', src=re.compile(r'card|pokemon', re.I)),
                    soup.find_all(['span', 'div'], text=re.compile(r'\d+/\d+', re.I)),
                    soup.find_all(['span', 'div'], class_=re.compile(r'number|rarity', re.I)),
                ]
                
                for i, indicators in enumerate(card_indicators):
                    if indicators:
                        print(f"  Indicateurs type {i+1}: {len(indicators)} trouvés")
                        for ind in indicators[:2]:
                            if ind.name == 'img':
                                print(f"    Image: {ind.get('src', '')}")
                            else:
                                print(f"    Texte: {ind.get_text(strip=True)}")
                
                return True
            
        except Exception as e:
            print(f"Erreur: {e}")
    
    return False

def check_mobile_vs_desktop():
    """Vérifier si le site a des versions mobile/desktop différentes"""
    print("\n📱 VÉRIFICATION MOBILE vs DESKTOP")
    print("=" * 50)
    
    url = "https://www.pokemon-card.com/card-search/index.php"
    
    user_agents = {
        'Desktop': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Mobile': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
        'Android': 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36'
    }
    
    for device, ua in user_agents.items():
        print(f"\n🔍 Test {device}:")
        
        headers = HEADERS.copy()
        headers['User-Agent'] = ua
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"Status: {response.status_code}")
            print(f"Taille réponse: {len(response.content)} bytes")
            
            # Chercher des différences dans le contenu
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Compter les éléments
            forms = len(soup.find_all('form'))
            inputs = len(soup.find_all('input'))
            selects = len(soup.find_all('select'))
            
            print(f"Forms: {forms}, Inputs: {inputs}, Selects: {selects}")
            
        except Exception as e:
            print(f"Erreur: {e}")

def main():
    print("🇯🇵 ANALYSE APPROFONDIE DU SITE JAPONAIS")
    print("=" * 60)
    
    # 1. Analyser la structure des résultats
    analyze_search_results_structure()
    
    time.sleep(2)
    
    # 2. Chercher des APIs
    find_api_endpoints()
    
    time.sleep(2)
    
    # 3. Analyser les patterns de détail
    analyze_card_detail_patterns()
    
    time.sleep(2)
    
    # 4. Vérifier mobile vs desktop
    check_mobile_vs_desktop()
    
    print(f"\n🎉 ANALYSE APPROFONDIE TERMINÉE")

if __name__ == "__main__":
    main()
