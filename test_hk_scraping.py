#!/usr/bin/env python3
"""
Test direct du scraping Traditional Chinese
"""

import sys
import os
sys.path.append('scraping')

from scraping_traditional_chinese import scrape_all_cards_progressive, DB_CONFIG
import mysql.connector

def test_scraping():
    print("🇭🇰 TEST DIRECT DU SCRAPING TRADITIONAL CHINESE")
    print("=" * 50)
    
    try:
        # Connexion à la base de données
        connection = mysql.connector.connect(**DB_CONFIG)
        print("✅ Connexion à la base de données établie")
        
        # Test direct de la fonction de scraping
        print("\n🎯 LANCEMENT DU SCRAPING DIRECT...")
        saved_count = scrape_all_cards_progressive(connection)
        
        print(f"\n🎉 TEST TERMINÉ: {saved_count} cartes sauvegardées")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        if 'connection' in locals() and connection.is_connected():
            connection.close()

if __name__ == "__main__":
    test_scraping()
