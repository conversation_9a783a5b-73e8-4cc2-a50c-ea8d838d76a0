#!/usr/bin/env python3
"""
Script de migration pour unifier la structure de base de données Pokemon TCG
- Migre les données de cards/card_versions vers pokemon_cards
- Nettoie les tables redondantes
- Assure la cohérence des données
"""

import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def migrate_database():
    """Migration complète de la base de données"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("🚀 DÉBUT DE LA MIGRATION DE BASE DE DONNÉES")
        print("=" * 60)
        
        # 1. Analyser l'état actuel
        print("\n📊 ANALYSE DE L'ÉTAT ACTUEL:")
        
        # Compter les enregistrements dans chaque table
        tables_to_check = ['cards', 'card_versions', 'pokemon_cards', 'expansions', 'pokemon_expansions']
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                count = cursor.fetchone()['count']
                print(f"   {table}: {count} enregistrements")
            except mysql.connector.Error as e:
                print(f"   {table}: Table non trouvée ou erreur ({e})")
        
        # 2. Migrer les données de cards/card_versions vers pokemon_cards
        print("\n🔄 MIGRATION DES DONNÉES:")
        
        # Migrer les cartes de l'ancienne structure
        cursor.execute("""
            SELECT 
                c.id as card_id,
                c.canonical_number,
                c.expansion_id,
                c.supertype,
                c.illustrator,
                cv.id as version_id,
                cv.language_id,
                cv.name,
                cv.web_id,
                cv.image_url,
                l.code as language_code
            FROM cards c
            LEFT JOIN card_versions cv ON c.id = cv.card_id
            LEFT JOIN languages l ON cv.language_id = l.id
            WHERE cv.id IS NOT NULL
        """)
        
        old_cards = cursor.fetchall()
        migrated_count = 0
        
        for card in old_cards:
            try:
                # Vérifier si cette carte existe déjà dans pokemon_cards
                cursor.execute("""
                    SELECT id FROM pokemon_cards 
                    WHERE web_id = %s AND source = %s
                """, (card['web_id'], f"migrated_{card['language_code']}"))
                
                existing = cursor.fetchone()
                if existing:
                    print(f"   ⚠️  Carte {card['name']} déjà migrée")
                    continue
                
                # Préparer les colonnes selon la langue
                name_column = f"name_{card['language_code']}" if card['language_code'] in ['en', 'th', 'id', 'ja', 'ko', 'zh', 'fr', 'de', 'it', 'es'] else 'name_en'
                image_column = f"image_url_{card['language_code']}" if card['language_code'] in ['en', 'th', 'id', 'ja', 'ko', 'zh', 'fr', 'de', 'it', 'es'] else 'image_url'
                
                # Insérer dans pokemon_cards
                insert_query = f"""
                    INSERT INTO pokemon_cards (
                        expansion_id, card_number, {name_column}, web_id, 
                        {image_column}, source, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                
                cursor.execute(insert_query, (
                    card['expansion_id'],
                    card['canonical_number'],
                    card['name'],
                    card['web_id'],
                    card['image_url'],
                    f"migrated_{card['language_code']}"
                ))
                
                migrated_count += 1
                print(f"   ✅ Migré: {card['name']} ({card['language_code']})")
                
            except mysql.connector.Error as e:
                print(f"   ❌ Erreur migration {card['name']}: {e}")
                continue
        
        connection.commit()
        print(f"\n📊 MIGRATION TERMINÉE: {migrated_count} cartes migrées")
        
        # 3. Nettoyer les tables redondantes (optionnel - commenté pour sécurité)
        print("\n🧹 NETTOYAGE DES TABLES:")
        print("   ⚠️  Nettoyage des anciennes tables désactivé pour sécurité")
        print("   💡 Pour nettoyer, décommentez les lignes dans le script")
        
        # Décommenter ces lignes SEULEMENT après vérification que la migration est OK
        # cursor.execute("DROP TABLE IF EXISTS card_versions")
        # cursor.execute("DROP TABLE IF EXISTS cards") 
        # cursor.execute("DROP TABLE IF EXISTS pokemon_expansions")
        # print("   ✅ Tables anciennes supprimées")
        
        # 4. Optimiser la table pokemon_cards
        print("\n⚡ OPTIMISATION:")
        
        # Ajouter des index si nécessaire
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_web_id_source ON pokemon_cards(web_id, source)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_expansion_id ON pokemon_cards(expansion_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_card_number ON pokemon_cards(card_number)")
            print("   ✅ Index ajoutés pour performance")
        except mysql.connector.Error as e:
            print(f"   ⚠️  Index déjà existants ou erreur: {e}")
        
        # 5. Statistiques finales
        print("\n📊 STATISTIQUES FINALES:")
        cursor.execute("SELECT COUNT(*) as count FROM pokemon_cards")
        final_count = cursor.fetchone()['count']
        print(f"   pokemon_cards: {final_count} enregistrements")
        
        cursor.execute("SELECT DISTINCT source FROM pokemon_cards")
        sources = cursor.fetchall()
        print(f"   Sources: {', '.join([s['source'] for s in sources])}")
        
        connection.commit()
        print("\n🎉 MIGRATION TERMINÉE AVEC SUCCÈS!")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ Erreur de base de données: {e}")
        if 'connection' in locals():
            connection.rollback()
        return False
    
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("🔌 Connexion fermée")

def verify_migration():
    """Vérifie que la migration s'est bien passée"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("\n🔍 VÉRIFICATION DE LA MIGRATION:")
        
        # Vérifier les données dans pokemon_cards
        cursor.execute("""
            SELECT 
                source, 
                COUNT(*) as count,
                COUNT(DISTINCT expansion_id) as expansions,
                COUNT(DISTINCT web_id) as unique_cards
            FROM pokemon_cards 
            GROUP BY source
        """)
        
        results = cursor.fetchall()
        for result in results:
            print(f"   {result['source']}: {result['count']} cartes, {result['expansions']} sets, {result['unique_cards']} cartes uniques")
        
        # Vérifier les expansions
        cursor.execute("SELECT COUNT(*) as count FROM expansions")
        exp_count = cursor.fetchone()['count']
        print(f"   Expansions: {exp_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur vérification: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    print("🎯 MIGRATION DE BASE DE DONNÉES POKEMON TCG")
    print("=" * 50)
    
    # Demander confirmation
    response = input("\n⚠️  Voulez-vous procéder à la migration ? (oui/non): ")
    if response.lower() not in ['oui', 'o', 'yes', 'y']:
        print("❌ Migration annulée")
        sys.exit(0)
    
    # Effectuer la migration
    success = migrate_database()
    
    if success:
        # Vérifier la migration
        verify_migration()
        print("\n✅ Migration terminée avec succès!")
    else:
        print("\n❌ Échec de la migration!")
        sys.exit(1)
