#!/usr/bin/env python3
"""
Test simple pour le scraper Traditional Chinese
"""

import requests
from bs4 import BeautifulSoup
import re

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def test_card_detail(card_id):
    """Tester la récupération d'une carte spécifique"""
    print(f"🔍 Test de la carte {card_id}...")
    
    url = f"https://asia.pokemon-card.com/hk/card-search/detail/{card_id}/"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extraire le titre de la page
            title = soup.find('title')
            if title:
                print(f"Titre: {title.get_text(strip=True)}")
            
            # Chercher le nom de la carte
            h1_elements = soup.find_all('h1')
            for h1 in h1_elements:
                text = h1.get_text(strip=True)
                if text:
                    print(f"H1: {text}")
            
            # Chercher des informations de carte
            card_info = soup.find_all(text=re.compile(r'\d+/\d+'))
            for info in card_info:
                print(f"Numéro possible: {info.strip()}")
            
            # Tester l'URL de l'image
            image_url = f"https://asia.pokemon-card.com/hk/card-img/hk{card_id:08d}.png"
            print(f"URL image: {image_url}")
            
            img_response = requests.head(image_url, headers=HEADERS, timeout=10)
            print(f"Status image: {img_response.status_code}")
            
            return True
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_search_page():
    """Tester la page de recherche"""
    print("🔍 Test de la page de recherche...")
    
    url = "https://asia.pokemon-card.com/hk/card-search/list/?expansionCodes=SV1S&pageNo=1"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Chercher des éléments de cartes
            card_elements = soup.find_all('div', class_='cardItem')
            print(f"Éléments cardItem trouvés: {len(card_elements)}")
            
            # Chercher d'autres patterns possibles
            links = soup.find_all('a', href=re.compile(r'/detail/\d+'))
            print(f"Liens vers détails trouvés: {len(links)}")
            
            if links:
                for i, link in enumerate(links[:3]):
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    print(f"  Lien {i+1}: {href} - {text}")
            
            return len(card_elements) > 0 or len(links) > 0
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_products_page():
    """Tester la page produits"""
    print("🔍 Test de la page produits...")
    
    url = "https://asia.pokemon-card.com/hk/products/"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Chercher des liens vers des produits
            product_links = soup.find_all('a', href=re.compile(r'/products/'))
            print(f"Liens produits trouvés: {len(product_links)}")
            
            # Chercher des codes de sets dans le texte
            page_text = soup.get_text()
            set_codes = re.findall(r'\b(SV\d+[a-zA-Z]*)\b', page_text)
            unique_codes = list(set(set_codes))
            print(f"Codes de sets trouvés: {unique_codes[:10]}")
            
            return len(product_links) > 0 or len(unique_codes) > 0
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🇭🇰 TEST DU SCRAPER TRADITIONAL CHINESE")
    print("=" * 50)
    
    # Test 1: Carte spécifique
    print("\n1. Test carte 13825:")
    test_card_detail(13825)
    
    # Test 2: Page de recherche
    print("\n2. Test page de recherche:")
    test_search_page()
    
    # Test 3: Page produits
    print("\n3. Test page produits:")
    test_products_page()
    
    print("\n✅ Tests terminés")
