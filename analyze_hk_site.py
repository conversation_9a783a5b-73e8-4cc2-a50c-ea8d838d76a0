#!/usr/bin/env python3
"""
Analyser les 15 premières pages du site Traditional Chinese
pour comprendre la structure et la logique
"""

import requests
from bs4 import BeautifulSoup
import re
import time

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def analyze_page(page_num):
    """Analyser une page spécifique"""
    print(f"\n📄 ANALYSE PAGE {page_num}")
    print("-" * 50)
    
    url = f"https://asia.pokemon-card.com/hk/card-search/list/?pageNo={page_num}"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Erreur HTTP {response.status_code}")
            return []
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        cards_found = []
        
        # Méthode 1: Chercher des éléments avec class contenant "card"
        card_elements = soup.find_all(attrs={'class': re.compile(r'card', re.I)})
        print(f"Éléments avec 'card' dans class: {len(card_elements)}")
        
        for elem in card_elements[:3]:  # Analyser les 3 premiers
            classes = elem.get('class', [])
            print(f"  - Element: {elem.name}, classes: {classes}")
        
        # Méthode 2: Chercher des liens vers /detail/
        detail_links = soup.find_all('a', href=re.compile(r'/detail/\d+'))
        print(f"Liens vers détails: {len(detail_links)}")
        
        for link in detail_links[:5]:  # Analyser les 5 premiers
            href = link.get('href', '')
            text = link.get_text(strip=True)
            
            # Extraire l'ID de la carte
            card_id_match = re.search(r'/detail/(\d+)', href)
            if card_id_match:
                card_id = card_id_match.group(1)
                
                # Chercher des infos dans le parent
                parent = link.parent
                card_info = {
                    'id': card_id,
                    'text': text,
                    'href': href
                }
                
                # Chercher le nom de la carte
                if parent:
                    name_elem = parent.find(text=True)
                    if name_elem:
                        card_info['name'] = name_elem.strip()
                
                cards_found.append(card_info)
                print(f"  ✅ Carte {card_id}: {text}")
        
        # Méthode 3: Chercher des images de cartes
        images = soup.find_all('img', src=re.compile(r'card-img|pokemon'))
        print(f"Images de cartes: {len(images)}")
        
        for img in images[:3]:
            src = img.get('src', '')
            alt = img.get('alt', '')
            print(f"  - Image: {src} (alt: {alt})")
        
        # Méthode 4: Analyser la structure générale
        print("\n🔍 STRUCTURE DE LA PAGE:")
        
        # Chercher des conteneurs principaux
        main_containers = soup.find_all(['div', 'section', 'article'], 
                                      attrs={'class': re.compile(r'main|content|list|grid', re.I)})
        print(f"Conteneurs principaux: {len(main_containers)}")
        
        for container in main_containers[:2]:
            classes = container.get('class', [])
            children = len(container.find_all())
            print(f"  - {container.name}: {classes} ({children} enfants)")
        
        # Chercher des patterns de sets dans la page
        page_text = soup.get_text()
        set_patterns = re.findall(r'\b([A-Z]{1,3}\d+[A-Z]{0,2})\b', page_text)
        unique_sets = list(set(set_patterns))[:10]  # Limiter à 10
        print(f"Patterns de sets trouvés: {unique_sets}")
        
        return cards_found
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def analyze_card_detail(card_id):
    """Analyser une page de détail de carte"""
    print(f"\n🔍 ANALYSE DÉTAIL CARTE {card_id}")
    print("-" * 30)
    
    url = f"https://asia.pokemon-card.com/hk/card-search/detail/{card_id}/"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Erreur HTTP {response.status_code}")
            return None
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        card_info = {'id': card_id}
        
        # Chercher le titre/nom
        title = soup.find('title')
        if title:
            card_info['title'] = title.get_text(strip=True)
            print(f"Titre: {card_info['title']}")
        
        # Chercher les h1, h2, h3
        for tag in ['h1', 'h2', 'h3']:
            elements = soup.find_all(tag)
            for elem in elements:
                text = elem.get_text(strip=True)
                if text:
                    print(f"{tag.upper()}: {text}")
        
        # Chercher des numéros de carte (format X/Y)
        card_numbers = soup.find_all(text=re.compile(r'\d+/\d+'))
        for num in card_numbers:
            print(f"Numéro possible: {num.strip()}")
        
        # Chercher des informations dans des spans, divs
        info_elements = soup.find_all(['span', 'div'], 
                                    attrs={'class': re.compile(r'number|rarity|type|hp', re.I)})
        for elem in info_elements:
            classes = elem.get('class', [])
            text = elem.get_text(strip=True)
            if text:
                print(f"Info ({classes}): {text}")
        
        # Tester l'URL de l'image
        image_url = f"https://asia.pokemon-card.com/hk/card-img/hk{int(card_id):08d}.png"
        print(f"URL image testée: {image_url}")
        
        img_response = requests.head(image_url, headers=HEADERS, timeout=10)
        print(f"Status image: {img_response.status_code}")
        
        return card_info
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def main():
    print("🇭🇰 ANALYSE COMPLÈTE DU SITE TRADITIONAL CHINESE")
    print("=" * 60)
    print("🎯 Objectif: Comprendre la structure pour améliorer le scraper")
    print("=" * 60)
    
    all_cards = []
    
    # Analyser les 15 premières pages
    for page in range(1, 16):
        cards = analyze_page(page)
        all_cards.extend(cards)
        
        # Pause entre les pages
        time.sleep(1)
        
        # Si pas de cartes trouvées, arrêter
        if not cards:
            print(f"❌ Aucune carte trouvée page {page}, arrêt de l'analyse")
            break
    
    print(f"\n📊 RÉSUMÉ GLOBAL:")
    print(f"Total cartes trouvées: {len(all_cards)}")
    
    # Analyser quelques cartes en détail
    if all_cards:
        print(f"\n🔍 ANALYSE DÉTAILLÉE DE 3 CARTES:")
        for card in all_cards[:3]:
            analyze_card_detail(card['id'])
            time.sleep(1)
    
    print(f"\n✅ ANALYSE TERMINÉE")

if __name__ == "__main__":
    main()
