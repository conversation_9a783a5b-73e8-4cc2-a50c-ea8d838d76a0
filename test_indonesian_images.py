#!/usr/bin/env python3
"""Test du téléchargement d'images indonésiennes"""

import sys
import os
sys.path.append('.')

from scraping.scraping_indonesian import create_card_post, download_image, generate_image_filename
from config.database import get_db_connection

def test_image_download():
    """Test simple du téléchargement d'images"""
    
    # Données de test d'une carte indonésienne
    test_card = {
        'webId': '15861',
        'nameCard': 'Pinsir <Ethan>',
        'numberCard': '001',
        'imageUrl': 'https://asia.pokemon-card.com/assets/images/card_images/large/SV10s/042_SV10s_SV10s_001.jpg',
        'rarity': 'Common'
    }
    
    set_code = 'SV10S'
    
    print("🧪 TEST DU TÉLÉCHARGEMENT D'IMAGES INDONÉSIENNES")
    print("=" * 60)
    
    # Test 1: Génération du nom de fichier
    print("\\n1️⃣ Test génération nom de fichier:")
    try:
        filename = generate_image_filename(test_card, set_code)
        print(f"   ✅ Nom généré: {filename}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return
    
    # Test 2: Téléchargement d'image
    print("\\n2️⃣ Test téléchargement image:")
    local_path = f"images/{set_code}_I/{filename}"
    print(f"   📁 Chemin local: {local_path}")
    
    try:
        success = download_image(test_card['imageUrl'], local_path)
        if success:
            print(f"   ✅ Image téléchargée avec succès!")
            print(f"   📂 Vérification existence: {os.path.exists(local_path)}")
            if os.path.exists(local_path):
                size = os.path.getsize(local_path)
                print(f"   📏 Taille: {size} bytes")
        else:
            print(f"   ❌ Échec du téléchargement")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 3: Création de carte avec image
    print("\\n3️⃣ Test création carte avec image:")
    try:
        connection = get_db_connection()
        card_id = create_card_post(connection, test_card, set_code)
        if card_id:
            print(f"   ✅ Carte créée avec ID: {card_id}")
        else:
            print(f"   ❌ Échec création carte")
        connection.close()
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    print("\\n🏁 Test terminé!")

if __name__ == "__main__":
    test_image_download()
