#!/usr/bin/env python3
"""
Direct Flask app runner - bypassing environment issues
"""

import os
import sys

# Clear any virtual environment
if 'VIRTUAL_ENV' in os.environ:
    del os.environ['VIRTUAL_ENV']

# Set up paths
project_root = '/media/devmon/piHDD/Sites/pokemon-tcg-scraper'
sys.path.insert(0, project_root)
os.chdir(os.path.join(project_root, 'scraping'))

print("🚀 Pokemon TCG Scraper - Starting Web Interface")
print("📂 Project root:", project_root)
print("📍 Current directory:", os.getcwd())
print("🐍 Python path:", sys.executable)
print("=" * 60)

try:
    # Test imports first
    print("📦 Testing imports...")
    
    import mysql.connector
    print("✅ MySQL connector imported")
    
    from flask import Flask
    print("✅ Flask imported")
    
    # Import configuration
    sys.path.append(project_root)
    from config.database import get_db_config
    print("✅ Database config imported")
    
    # Test database connection
    config = get_db_config()
    connection = mysql.connector.connect(**config)
    connection.close()
    print("✅ Database connection successful")
    
    # Import the Flask app
    from app import app
    print("✅ Flask app imported")
    
    print("\n🌐 Starting web server...")
    print("📍 URL: http://127.0.0.1:5051/")
    print("🔧 Debug mode: ON")
    print("⚡ Threaded: ON")
    print("=" * 60)
    
    # Start the Flask application
    app.run(
        host='127.0.0.1',
        port=5051,
        debug=True,
        use_reloader=False,
        threaded=True
    )
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Try installing missing dependencies with: pip3 install -r requirements.txt")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    
print("\n👋 Server stopped")
