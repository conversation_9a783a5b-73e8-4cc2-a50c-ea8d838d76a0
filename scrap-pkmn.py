#!/usr/bin/env python3
"""
Pokemon TCG Scraper - Launcher avec Menu Interactif
Usage: python3 scrap-pkmn.py
"""

import os
import sys
import subprocess

# Obtenir le répertoire du projet dynamiquement
project_root = os.path.dirname(os.path.abspath(__file__))
venv_path = os.path.join(project_root, 'venv')

# Activer l'environnement virtuel automatiquement
if os.path.exists(venv_path) and 'VIRTUAL_ENV' not in os.environ:
    # Activer l'environnement virtuel
    activate_script = os.path.join(venv_path, 'bin', 'activate_this.py')
    if os.path.exists(activate_script):
        exec(open(activate_script).read(), {'__file__': activate_script})
    else:
        # Méthode alternative : modifier le PATH et PYTHONPATH
        os.environ['VIRTUAL_ENV'] = venv_path
        os.environ['PATH'] = f"{venv_path}/bin:{os.environ.get('PATH', '')}"
        sys.path.insert(0, f"{venv_path}/lib/python{sys.version_info.major}.{sys.version_info.minor}/site-packages")

# Configurer le path
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'scraping'))

def show_menu():
    print("\n🃏 POKEMON TCG SCRAPER")
    print("=" * 40)
    print("1. 🌐 Démarrer le serveur web")
    print("2. 🇹🇭 Scraper thaï")
    print("3. 🇮🇩 Scraper indonésien")
    print("4. 🇭🇰 Scraper Traditional Chinese")
    print("5. 🇯🇵 Scraper japonais")
    print("6. 📚 Scraper Bulbapedia")
    print("7. 🔗 Système de liaison multilingue")
    print("8. 📊 Voir les statistiques")
    print("9. ❌ Quitter")
    print("=" * 40)

def start_server():
    print("🚀 Démarrage du serveur...")
    print("🌐 URL Locale: http://127.0.0.1:5051/")
    print("🌐 URL Réseau: http://*************:5051/")
    print("💡 Appuyez sur Ctrl+C pour arrêter")
    print("=" * 40)

    # Aller dans le bon répertoire
    project_root = os.path.dirname(os.path.abspath(__file__))
    os.chdir(os.path.join(project_root, 'scraping'))

    # Lancer l'application
    from app import app
    app.run(host='0.0.0.0', port=5051, debug=False, use_reloader=False, threaded=True)

def start_thai_scraper():
    print("🇹🇭 Lancement du scraper thaï...")
    print("🔥 SCRAPING COMPLET - TOUS LES SETS ET TOUTES LES CARTES")
    project_root = os.path.dirname(os.path.abspath(__file__))
    os.chdir(os.path.join(project_root, 'scraping'))
    # Utiliser le Python de l'environnement virtuel
    python_exe = os.path.join(project_root, 'venv', 'bin', 'python3')
    if not os.path.exists(python_exe):
        python_exe = sys.executable
    subprocess.run([python_exe, 'scraping_thai.py'])

def start_indonesian_scraper():
    print("🇮🇩 Lancement du scraper indonésien...")
    print("🔥 SCRAPING COMPLET - TOUS LES SETS ET TOUTES LES CARTES")
    project_root = os.path.dirname(os.path.abspath(__file__))
    os.chdir(os.path.join(project_root, 'scraping'))
    # Utiliser le Python de l'environnement virtuel
    python_exe = os.path.join(project_root, 'venv', 'bin', 'python3')
    if not os.path.exists(python_exe):
        python_exe = sys.executable
    subprocess.run([python_exe, 'scraping_indonesian.py'])

def start_traditional_chinese_scraper():
    print("🇭🇰 Lancement du scraper Traditional Chinese...")
    print("🔥 SCRAPING COMPLET - TOUS LES SETS ET TOUTES LES CARTES")
    project_root = os.path.dirname(os.path.abspath(__file__))
    os.chdir(os.path.join(project_root, 'scraping'))
    # Utiliser le Python de l'environnement virtuel
    python_exe = os.path.join(project_root, 'venv', 'bin', 'python3')
    if not os.path.exists(python_exe):
        python_exe = sys.executable
    subprocess.run([python_exe, 'scraping_traditional_chinese.py'])

def start_japanese_scraper():
    print("🇯🇵 Lancement du scraper japonais...")
    print("🔥 SCRAPING COMPLET - TOUTES LES RÉGULATIONS ET TOUTES LES CARTES")
    project_root = os.path.dirname(os.path.abspath(__file__))
    os.chdir(os.path.join(project_root, 'scraping'))
    # Utiliser le Python de l'environnement virtuel
    python_exe = os.path.join(project_root, 'venv', 'bin', 'python3')
    if not os.path.exists(python_exe):
        python_exe = sys.executable
    subprocess.run([python_exe, 'scraping_japanese.py'])

def start_bulbapedia_scraper():
    print("📚 Lancement du scraper Bulbapedia...")
    print("🔥 SCRAPING COMPLET - TOUS LES SETS ET TOUTES LES CARTES")
    print("✅ Utilisation du scraper original qui fonctionnait sur Mac")
    print("📋 Récupération de TOUS les sets de Bulbapedia")
    print("=" * 60)

    project_root = os.path.dirname(os.path.abspath(__file__))
    os.chdir(os.path.join(project_root, 'scraping'))
    # Utiliser le Python de l'environnement virtuel
    python_exe = os.path.join(project_root, 'venv', 'bin', 'python3')
    if not os.path.exists(python_exe):
        python_exe = sys.executable
    subprocess.run([python_exe, 'parse_bulbapedia.py'])

def run_linking_system():
    print("🔗 Lancement du système de liaison multilingue...")
    print("💡 Ce système va analyser vos données et créer les liens entre cartes")
    print("=" * 60)
    try:
        project_root = os.path.dirname(os.path.abspath(__file__))
        os.chdir(project_root)
        python_exe = os.path.join(project_root, 'venv', 'bin', 'python3')
        if not os.path.exists(python_exe):
            python_exe = sys.executable
        subprocess.run([python_exe, 'utils/smart_card_linking.py'])
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de la liaison: {e}")
    except FileNotFoundError:
        print("❌ Fichier smart_card_linking.py non trouvé")

    input("\n📋 Appuyez sur Entrée pour continuer...")

def show_stats():
    print("📊 Statistiques de la base de données...")
    # Ici on pourrait ajouter des stats de la DB
    print("Fonctionnalité à venir...")

def main():
    while True:
        show_menu()
        try:
            choice = input("\nChoisissez une option (1-9): ").strip()

            if choice == '1':
                start_server()
                break
            elif choice == '2':
                start_thai_scraper()
            elif choice == '3':
                start_indonesian_scraper()
            elif choice == '4':
                start_traditional_chinese_scraper()
            elif choice == '5':
                start_japanese_scraper()
            elif choice == '6':
                start_bulbapedia_scraper()
            elif choice == '7':
                run_linking_system()
            elif choice == '8':
                show_stats()
            elif choice == '9':
                print("👋 Au revoir !")
                break
            else:
                print("❌ Option invalide. Choisissez entre 1 et 9.")

        except KeyboardInterrupt:
            print("\n👋 Au revoir !")
            break
        except Exception as e:
            print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
