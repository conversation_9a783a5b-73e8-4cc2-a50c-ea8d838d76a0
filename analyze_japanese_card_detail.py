#!/usr/bin/env python3
"""
Analyse d'une page de détail de carte japonaise
pour comprendre la structure et extraire les données
"""

import requests
from bs4 import BeautifulSoup
import re
import time

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
    'Referer': 'https://www.pokemon-card.com/',
    'Connection': 'keep-alive'
}

def analyze_card_detail(card_id, regulation):
    """Analyser une page de détail de carte spécifique"""
    print(f"🔍 ANALYSE CARTE {card_id} (RÉGULATION {regulation})")
    print("=" * 60)
    
    url = f"https://www.pokemon-card.com/card-search/details.php/card/{card_id}/regu/{regulation}"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        print(f"Status: {response.status_code}")
        print(f"URL: {url}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            card_data = {}
            
            # 1. Extraire le titre/nom de la carte
            title = soup.find('title')
            if title:
                title_text = title.get_text(strip=True)
                print(f"📝 Titre page: {title_text}")
                
                # Extraire le nom de la carte du titre
                if 'カード詳細（' in title_text and '）' in title_text:
                    card_name = title_text.split('カード詳細（')[1].split('）')[0]
                    card_data['name'] = card_name
                    print(f"📝 Nom carte: {card_name}")
            
            # 2. Chercher l'image de la carte
            card_images = []
            for img in soup.find_all('img'):
                src = img.get('src', '')
                alt = img.get('alt', '')
                
                # Patterns d'images de cartes japonaises
                if any(pattern in src for pattern in ['/card/', '/cards/', 'pokemon', 'tcg']):
                    card_images.append({
                        'src': src,
                        'alt': alt
                    })
                    print(f"🖼️  Image: {src}")
                    print(f"    Alt: {alt}")
            
            card_data['images'] = card_images
            
            # 3. Chercher les informations de la carte
            print(f"\n📋 INFORMATIONS DE LA CARTE:")
            
            # Chercher des éléments avec des classes spécifiques
            info_selectors = [
                {'selector': 'span', 'class': re.compile(r'number|rarity|type|hp', re.I)},
                {'selector': 'div', 'class': re.compile(r'card.*info|detail', re.I)},
                {'selector': 'td', 'text': True},
                {'selector': 'th', 'text': True},
            ]
            
            for selector_info in info_selectors:
                elements = soup.find_all(selector_info['selector'])
                if 'class' in selector_info:
                    elements = [e for e in elements if e.get('class') and selector_info['class'].search(' '.join(e.get('class', [])))]
                
                for elem in elements[:10]:  # Limiter l'affichage
                    text = elem.get_text(strip=True)
                    classes = elem.get('class', [])
                    if text and len(text) < 100:
                        print(f"  {elem.name} ({classes}): {text}")
            
            # 4. Chercher des patterns de numéros de carte
            page_text = soup.get_text()
            
            # Patterns japonais courants
            number_patterns = [
                r'\b(\d{1,3}/\d{1,3})\b',  # 001/100, 25/78, etc.
                r'\b([A-Z]+\d+[A-Z]*)\b',  # XY1, SM2, SV3, etc.
            ]
            
            print(f"\n🔢 PATTERNS NUMÉROS DÉTECTÉS:")
            for pattern in number_patterns:
                matches = re.findall(pattern, page_text)
                unique_matches = list(set(matches))
                for match in unique_matches[:5]:  # Limiter l'affichage
                    print(f"  Pattern: {match}")
            
            # 5. Analyser la structure HTML pour trouver des conteneurs de données
            print(f"\n🏗️  STRUCTURE HTML:")
            
            # Chercher des conteneurs avec des IDs ou classes significatifs
            containers = soup.find_all(['div', 'section', 'article'], 
                                     attrs={'class': re.compile(r'card|detail|info|content', re.I)})
            
            for i, container in enumerate(containers[:5]):
                classes = container.get('class', [])
                container_id = container.get('id', '')
                children = len(container.find_all())
                print(f"  Conteneur {i+1}: {classes} (ID: {container_id}, {children} enfants)")
                
                # Chercher des données structurées dans ce conteneur
                data_elements = container.find_all(['span', 'div', 'p'], limit=5)
                for elem in data_elements:
                    text = elem.get_text(strip=True)
                    if text and len(text) < 50:
                        print(f"    - {text}")
            
            # 6. Chercher des métadonnées dans les balises meta
            print(f"\n🏷️  MÉTADONNÉES:")
            meta_tags = soup.find_all('meta')
            for meta in meta_tags:
                name = meta.get('name', '') or meta.get('property', '')
                content = meta.get('content', '')
                if name and content and any(keyword in name.lower() for keyword in ['title', 'description', 'image']):
                    print(f"  {name}: {content}")
            
            return card_data
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_multiple_cards():
    """Tester plusieurs cartes pour comprendre les patterns"""
    print(f"\n🧪 TEST MULTIPLE CARTES")
    print("=" * 50)
    
    # Tester différentes cartes et régulations
    test_cards = [
        {'id': 47537, 'regulation': 'XY'},
        {'id': 47538, 'regulation': 'XY'},
        {'id': 47539, 'regulation': 'XY'},
        {'id': 1, 'regulation': 'SV'},
        {'id': 100, 'regulation': 'SV'},
    ]
    
    for card in test_cards:
        print(f"\n🔍 Test carte {card['id']} ({card['regulation']}):")
        
        url = f"https://www.pokemon-card.com/card-search/details.php/card/{card['id']}/regu/{card['regulation']}"
        
        try:
            response = requests.get(url, headers=HEADERS, timeout=10)
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extraire le nom rapidement
                title = soup.find('title')
                if title:
                    title_text = title.get_text(strip=True)
                    if 'カード詳細（' in title_text:
                        card_name = title_text.split('カード詳細（')[1].split('）')[0]
                        print(f"  ✅ Nom: {card_name}")
                    else:
                        print(f"  📝 Titre: {title_text}")
                
                # Chercher des images
                images = soup.find_all('img', src=re.compile(r'card|pokemon'))
                print(f"  🖼️  Images: {len(images)} trouvées")
                
            else:
                print(f"  ❌ Erreur: {response.status_code}")
        
        except Exception as e:
            print(f"  ❌ Erreur: {e}")
        
        time.sleep(1)  # Pause entre les requêtes

def discover_id_ranges():
    """Découvrir les plages d'IDs valides"""
    print(f"\n🔍 DÉCOUVERTE PLAGES D'IDS")
    print("=" * 50)
    
    regulations = ['SV', 'SM', 'XY']
    
    for regulation in regulations:
        print(f"\n📊 Régulation {regulation}:")
        
        # Tester quelques IDs pour voir les plages
        test_ids = [1, 10, 100, 1000, 10000, 47537]
        valid_ids = []
        
        for test_id in test_ids:
            url = f"https://www.pokemon-card.com/card-search/details.php/card/{test_id}/regu/{regulation}"
            
            try:
                response = requests.get(url, headers=HEADERS, timeout=5)
                if response.status_code == 200:
                    valid_ids.append(test_id)
                    print(f"  ✅ ID {test_id}: Valide")
                else:
                    print(f"  ❌ ID {test_id}: {response.status_code}")
            except:
                print(f"  ❌ ID {test_id}: Erreur")
            
            time.sleep(0.5)
        
        print(f"  📊 IDs valides pour {regulation}: {valid_ids}")

def main():
    print("🇯🇵 ANALYSE DÉTAILLÉE CARTE JAPONAISE")
    print("=" * 60)
    
    # 1. Analyser la carte d'exemple
    card_data = analyze_card_detail(47537, 'XY')
    
    time.sleep(2)
    
    # 2. Tester plusieurs cartes
    test_multiple_cards()
    
    time.sleep(2)
    
    # 3. Découvrir les plages d'IDs
    discover_id_ranges()
    
    print(f"\n🎉 ANALYSE TERMINÉE")
    
    if card_data:
        print(f"\n📊 DONNÉES EXTRAITES:")
        for key, value in card_data.items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
