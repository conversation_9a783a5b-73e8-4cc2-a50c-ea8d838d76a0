-- Po<PERSON>mon TCG Database Schema - Version Fonctionnelle
-- <PERSON><PERSON><PERSON> sur l'analyse du code des scrapers actuels
-- Compatible avec MariaDB/MySQL

-- Utiliser la base de données
USE pokemon_tcg_db;

-- 1. Table des langues (utilisée par tous les scrapers)
CREATE TABLE IF NOT EXISTS `languages` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(5) UNIQUE NOT NULL,
    `name` VARCHAR(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Table des expansions/sets (utilisée par tous les scrapers)
CREATE TABLE IF NOT EXISTS `expansions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(20) UNIQUE NOT NULL,
    `name_en` VARCHAR(255),
    `name_id` VA<PERSON><PERSON><PERSON>(255),
    `name_th` VARCHAR(255),
    `release_date` DATE,
    `region` VARCHAR(50),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Table des illustrateurs (utilisée par les scrapers)
CREATE TABLE IF NOT EXISTS `pokemon_illustrators` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY `unique_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Table des cartes principales (utilisée par tous les scrapers)
CREATE TABLE IF NOT EXISTS `pokemon_cards` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `expansion_id` INT,
    `card_number` VARCHAR(50),
    `illustrator_id` INT,
    `source` VARCHAR(255),
    `linked_card_id` INT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`expansion_id`) REFERENCES `expansions`(`id`),
    FOREIGN KEY (`illustrator_id`) REFERENCES `pokemon_illustrators`(`id`),
    INDEX `idx_expansion` (`expansion_id`),
    INDEX `idx_card_number` (`card_number`),
    INDEX `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Table des versions de cartes par langue (utilisée par scrapers thai/indonesian)
CREATE TABLE IF NOT EXISTS `card_versions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `card_id` INT NOT NULL,
    `language_id` INT NOT NULL,
    `name` VARCHAR(255),
    `rarity` VARCHAR(50),
    `image_url` TEXT,
    `web_id` VARCHAR(100),
    `source` VARCHAR(50),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`card_id`) REFERENCES `pokemon_cards`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`language_id`) REFERENCES `languages`(`id`),
    INDEX `idx_card_language` (`card_id`, `language_id`),
    INDEX `idx_web_id` (`web_id`),
    INDEX `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Table des correspondances de sets (utilisée par bulbapedia_set_mappings.py)
CREATE TABLE IF NOT EXISTS `set_mappings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `bulbapedia_name` VARCHAR(255) NOT NULL,
    `english_name` VARCHAR(255),
    `japanese_name` VARCHAR(255),
    `thai_name` VARCHAR(255),
    `indonesian_name` VARCHAR(255),
    `korean_name` VARCHAR(255),
    `chinese_traditional_name` VARCHAR(255),
    `chinese_simplified_name` VARCHAR(255),
    `english_code` VARCHAR(50),
    `japanese_code` VARCHAR(50),
    `thai_code` VARCHAR(50),
    `indonesian_code` VARCHAR(50),
    `korean_code` VARCHAR(50),
    `chinese_traditional_code` VARCHAR(50),
    `chinese_simplified_code` VARCHAR(50),
    `series` VARCHAR(100),
    `release_year` INT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `unique_bulbapedia` (`bulbapedia_name`),
    INDEX `idx_english_name` (`english_name`),
    INDEX `idx_series` (`series`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertion des langues de base
INSERT IGNORE INTO `languages` (`code`, `name`) VALUES
('en', 'English'),
('th', 'ไทย (Thai)'),
('id', 'Bahasa Indonesia'),
('ja', '日本語 (Japanese)'),
('ko', '한국어 (Korean)'),
('zh', '中文 (Chinese)'),
('fr', 'Français'),
('de', 'Deutsch'),
('es', 'Español'),
('it', 'Italiano');

-- Index pour performance
CREATE INDEX IF NOT EXISTS `idx_card_versions_lookup` ON `card_versions` (`language_id`, `web_id`);
CREATE INDEX IF NOT EXISTS `idx_pokemon_cards_lookup` ON `pokemon_cards` (`expansion_id`, `card_number`);

-- Vue pour compatibilité avec l'interface web
CREATE OR REPLACE VIEW `scraping_stats` AS
SELECT 
    e.code as expansion_code,
    e.name_en as expansion_name,
    l.code as language_code,
    l.name as language_name,
    COUNT(DISTINCT pc.id) as total_cards,
    COUNT(DISTINCT cv.id) as scraped_cards,
    ROUND((COUNT(DISTINCT cv.id) / COUNT(DISTINCT pc.id)) * 100, 2) as completion_percentage
FROM expansions e
LEFT JOIN pokemon_cards pc ON e.id = pc.expansion_id
LEFT JOIN card_versions cv ON pc.id = cv.card_id
LEFT JOIN languages l ON cv.language_id = l.id
GROUP BY e.id, e.code, e.name_en, l.id, l.code, l.name
HAVING total_cards > 0
ORDER BY e.code, l.code;

-- Afficher les tables créées
SHOW TABLES;

-- Afficher les langues insérées
SELECT * FROM languages;

-- Message de confirmation
SELECT 'Base de données Pokemon TCG créée avec succès!' as status;
