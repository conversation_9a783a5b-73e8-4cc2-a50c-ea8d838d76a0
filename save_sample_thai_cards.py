#!/usr/bin/env python3
"""
Sauvegarde d'un échantillon des cartes Thai récupérées pour démonstration
"""

import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def get_language_id(connection, language_code):
    """Récupère l'ID de la langue"""
    cursor = connection.cursor(dictionary=True)
    cursor.execute("SELECT id FROM languages WHERE code = %s", (language_code,))
    result = cursor.fetchone()
    return result['id'] if result else None

def get_or_create_expansion(connection, set_code, set_name_en=None):
    """Récupère ou crée une expansion dans la nouvelle table expansions"""
    cursor = connection.cursor(dictionary=True)

    # Vérifier si l'expansion existe déjà
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    existing_expansion = cursor.fetchone()

    if existing_expansion:
        return existing_expansion['id']

    # Créer la nouvelle expansion
    cursor.execute("""
        INSERT INTO expansions (code, name_en, region)
        VALUES (%s, %s, 'Thailand')
    """, (set_code, set_name_en))

    connection.commit()
    return cursor.lastrowid

def get_or_create_card(connection, expansion_id, card_number, supertype='Pokémon'):
    """Récupère ou crée une carte maître"""
    cursor = connection.cursor(dictionary=True)
    
    # Chercher une carte existante avec ce numéro dans cette expansion
    cursor.execute("""
        SELECT id FROM pokemon_cards
        WHERE card_number = %s AND expansion_id = %s
    """, (card_number, expansion_id))
    
    existing_card = cursor.fetchone()
    if existing_card:
        return existing_card['id']
    
    # Créer une nouvelle carte maître
    cursor.execute("""
        INSERT INTO pokemon_cards (card_number, expansion_id, source)
        VALUES (%s, %s, %s)
    """, (card_number, expansion_id, 'sample'))
    
    connection.commit()
    return cursor.lastrowid

def save_sample_cards():
    """Sauvegarde un échantillon des cartes récupérées par le scraper"""
    
    print("🇹🇭 SAUVEGARDE ÉCHANTILLON CARTES THAI RÉCUPÉRÉES")
    print("=" * 60)
    
    # Connexion base de données
    try:
        connection = mysql.connector.connect(**get_db_config())
        print("✅ Connexion à la base de données établie")
    except Exception as e:
        print(f"❌ Erreur connexion base: {e}")
        return
    
    # Récupérer l'ID de la langue thaï
    thai_language_id = get_language_id(connection, 'th')
    if not thai_language_id:
        print("❌ Erreur: Langue thaï non trouvée dans la base")
        return
    
    # Échantillon des cartes récupérées par le scraper (basé sur la sortie)
    sample_cards = [
        # Set SV6A (Night Wanderer) - 001/154 à 154/154
        {'web_id': '1', 'name_th': 'สไตรค์', 'card_number': '001/154', 'set_code': 'SV6A', 'set_name_en': 'Night Wanderer'},
        {'web_id': '16', 'name_th': 'คาปู โคเคโคV', 'card_number': '016/154', 'set_code': 'SV6A', 'set_name_en': 'Night Wanderer'},
        {'web_id': '50', 'name_th': 'พอตเดธV', 'card_number': '050/154', 'set_code': 'SV6A', 'set_name_en': 'Night Wanderer'},
        {'web_id': '100', 'name_th': 'ฮัซซัม', 'card_number': '100/154', 'set_code': 'SV6A', 'set_name_en': 'Night Wanderer'},
        {'web_id': '154', 'name_th': 'ออโรราเอนเนอร์จี้', 'card_number': '154/154', 'set_code': 'SV6A', 'set_name_en': 'Night Wanderer'},
        
        # Set SV5A (Crimson Haze) - 001/153 à 153/153
        {'web_id': '155', 'name_th': 'คาเตอร์ปี', 'card_number': '001/153', 'set_code': 'SV5A', 'set_name_en': 'Crimson Haze'},
        {'web_id': '200', 'name_th': 'ฮิโตโมชิ', 'card_number': '046/153', 'set_code': 'SV5A', 'set_name_en': 'Crimson Haze'},
        {'web_id': '250', 'name_th': 'กาลาร์ จิกุซากุมะ', 'card_number': '096/153', 'set_code': 'SV5A', 'set_name_en': 'Crimson Haze'},
        {'web_id': '300', 'name_th': 'โซเนีย', 'card_number': '146/153', 'set_code': 'SV5A', 'set_name_en': 'Crimson Haze'},
        {'web_id': '307', 'name_th': 'ทวินเอนเนอร์จี้', 'card_number': '153/153', 'set_code': 'SV5A', 'set_name_en': 'Crimson Haze'},
        
        # Set SV4A (Paradox Rift) - 001/164 à 164/164
        {'web_id': '308', 'name_th': 'สไตรค์', 'card_number': '001/164', 'set_code': 'SV4A', 'set_name_en': 'Paradox Rift'},
        {'web_id': '350', 'name_th': 'คาจิริกาเมะ', 'card_number': '043/164', 'set_code': 'SV4A', 'set_name_en': 'Paradox Rift'},
        {'web_id': '400', 'name_th': 'ซึนะเฮบิ', 'card_number': '093/164', 'set_code': 'SV4A', 'set_name_en': 'Paradox Rift'},
        {'web_id': '450', 'name_th': 'ไบวูลู', 'card_number': '143/164', 'set_code': 'SV4A', 'set_name_en': 'Paradox Rift'},
        {'web_id': '470', 'name_th': 'แมรี', 'card_number': '163/164', 'set_code': 'SV4A', 'set_name_en': 'Paradox Rift'},
    ]
    
    saved_count = 0
    
    for card in sample_cards:
        print(f"\n📝 Sauvegarde: {card['name_th']} ({card['card_number']}) - Set {card['set_code']}")
        
        # Récupérer ou créer l'expansion
        expansion_id = get_or_create_expansion(
            connection,
            card['set_code'],
            card['set_name_en']
        )

        # Récupérer ou créer la carte maître
        card_id = get_or_create_card(
            connection,
            expansion_id,
            card['card_number']
        )

        # Créer la version thaï de la carte
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO card_versions (
                card_id, language_id, name, web_id
            ) VALUES (%s, %s, %s, %s)
        """, (
            card_id,
            thai_language_id,
            card['name_th'],
            card['web_id']
        ))

        saved_count += 1
        print(f"   ✅ Version thaï créée avec ID: {cursor.lastrowid}")

    connection.commit()
    connection.close()
    
    print(f"\n✅ SAUVEGARDE TERMINÉE: {saved_count} cartes thaï sauvegardées")
    
    # Vérifier les données finales
    print(f"\n🔍 VÉRIFICATION FINALE:")
    connection = mysql.connector.connect(**get_db_config())
    cursor = connection.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM expansions")
    expansions_count = cursor.fetchone()[0]
    print(f"   • Expansions: {expansions_count}")
    
    cursor.execute("SELECT COUNT(*) FROM cards")
    cards_count = cursor.fetchone()[0]
    print(f"   • Cartes maîtres: {cards_count}")
    
    cursor.execute("SELECT COUNT(*) FROM card_versions WHERE language_id = %s", (thai_language_id,))
    thai_versions_count = cursor.fetchone()[0]
    print(f"   • Versions thaï: {thai_versions_count}")
    
    # Afficher les expansions créées
    cursor.execute("SELECT code, name_en, COUNT(c.id) as card_count FROM expansions e LEFT JOIN cards c ON e.id = c.expansion_id GROUP BY e.id ORDER BY e.id")
    expansions = cursor.fetchall()
    print(f"\n📦 Expansions créées:")
    for exp in expansions:
        print(f"   • {exp[0]} - {exp[1]} ({exp[2]} cartes)")
    
    connection.close()

if __name__ == "__main__":
    save_sample_cards()
