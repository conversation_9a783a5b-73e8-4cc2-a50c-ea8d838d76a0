#!/usr/bin/env python3
"""
Script pour corriger les noms de tables dans app.py
Remplace pokemon_expansions par expansions et pokemon_cards par cards
"""

import re

def fix_table_names():
    """Corrige les noms de tables dans app.py"""
    
    # <PERSON><PERSON> le fichier
    with open('scraping/app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔧 Correction des noms de tables...")
    
    # Remplacements
    replacements = [
        ('pokemon_expansions', 'expansions'),
        ('pokemon_cards', 'cards'),
    ]
    
    changes_made = 0
    
    for old_name, new_name in replacements:
        old_count = content.count(old_name)
        content = content.replace(old_name, new_name)
        new_count = content.count(old_name)
        
        if old_count > new_count:
            changes_made += (old_count - new_count)
            print(f"✅ {old_name} -> {new_name} ({old_count - new_count} remplacements)")
    
    # <PERSON><PERSON><PERSON><PERSON> le fichier corrigé
    with open('scraping/app.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"🎯 Total: {changes_made} corrections effectuées")
    print("✅ Fichier app.py corrigé!")

if __name__ == "__main__":
    fix_table_names()
