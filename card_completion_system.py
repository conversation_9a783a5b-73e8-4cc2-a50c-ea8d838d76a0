#!/usr/bin/env python3
"""
Système de complétion automatique des cartes Pokemon TCG
"""

import mysql.connector
import json
from config.database import get_db_config


def complete_card_data(card_id, force_refresh=False):
    """
    Complète automatiquement les données manquantes d'une carte
    
    Args:
        card_id (int): ID de la carte à compléter
        force_refresh (bool): Force la recherche même si la carte semble complète
    
    Returns:
        dict: Carte complétée avec métadonnées de fallback
    """
    
    config = get_db_config()
    conn = mysql.connector.connect(**config)
    cursor = conn.cursor(dictionary=True)
    
    try:
        # Récupérer la carte originale
        cursor.execute("""
            SELECT c.*, e.code as set_code, e.name as set_name
            FROM pokemon_cards c
            JOIN pokemon_expansions e ON c.expansion_id = e.id
            WHERE c.id = %s
        """, (card_id,))
        
        original_card = cursor.fetchone()
        if not original_card:
            return None
        
        # Identifier les champs manquants
        missing_fields = []
        if not original_card.get('image_url') or original_card['image_url'].strip() == '':
            missing_fields.append('image_url')
        if not original_card.get('name_en') or original_card['name_en'].strip() == '':
            missing_fields.append('name_en')
        if not original_card.get('name_th') or original_card['name_th'].strip() == '':
            missing_fields.append('name_th')
        if not original_card.get('name_id') or original_card['name_id'].strip() == '':
            missing_fields.append('name_id')
        if not original_card.get('rarity') or original_card['rarity'].strip() == '':
            missing_fields.append('rarity')
        
        if not missing_fields and not force_refresh:
            return {**original_card, 'is_complete': True, 'fallback_used': False}
        
        # Chercher des cartes candidates pour le fallback
        # 1. D'abord dans les fallbacks explicites
        cursor.execute("""
            SELECT fc.*, c2.*, e2.code as fallback_set_code
            FROM card_fallbacks fc
            JOIN pokemon_cards c2 ON fc.fallback_card_id = c2.id
            JOIN expansions e2 ON c2.expansion_id = e2.id
            WHERE fc.card_id = %s
            ORDER BY fc.priority ASC
        """, (card_id,))
        
        explicit_fallbacks = cursor.fetchall()
        
        # 2. Ensuite dans le même groupe de sets (automatique)
        cursor.execute("""
            SELECT c2.*, e2.code as fallback_set_code, sgl.language_priority
            FROM pokemon_cards c2
            JOIN pokemon_expansions e2 ON c2.expansion_id = e2.id
            JOIN set_group_links sgl ON e2.id = sgl.set_id
            WHERE sgl.set_group_id IN (
                SELECT sgl2.set_group_id 
                FROM set_group_links sgl2 
                WHERE sgl2.set_id = %s
            )
            AND c2.card_number = %s
            AND c2.id != %s
            ORDER BY sgl.language_priority ASC
        """, (original_card['expansion_id'], original_card['card_number'], card_id))
        
        automatic_fallbacks = cursor.fetchall()
        
        # Combiner et prioriser les fallbacks
        all_fallbacks = []
        
        # Priorité aux fallbacks explicites
        for fb in explicit_fallbacks:
            all_fallbacks.append({
                'card': fb,
                'priority': fb['priority'],
                'type': 'explicit'
            })
        
        # Puis aux fallbacks automatiques
        for fb in automatic_fallbacks:
            all_fallbacks.append({
                'card': fb,
                'priority': fb['language_priority'],
                'type': 'automatic'
            })
        
        # Trier par priorité (plus petit = plus prioritaire)
        all_fallbacks.sort(key=lambda x: x['priority'])
        
        # Compléter les champs manquants
        completed_card = dict(original_card)
        completion_sources = {}
        
        for field in missing_fields:
            for fallback in all_fallbacks:
                fallback_card = fallback['card']
                if fallback_card.get(field) and fallback_card[field].strip():
                    completed_card[field] = fallback_card[field]
                    completion_sources[field] = {
                        'source_card_id': fallback_card['id'],
                        'source_set': fallback_card['fallback_set_code'],
                        'type': fallback['type']
                    }
                    break
        
        # Logger la complétion si des champs ont été complétés
        if completion_sources:
            # Choisir la source principale (première utilisée)
            main_source = list(completion_sources.values())[0]
            
            cursor.execute("""
                INSERT INTO fallback_completions 
                (card_id, source_card_id, completed_fields, completion_type)
                VALUES (%s, %s, %s, %s)
            """, (
                card_id,
                main_source['source_card_id'],
                json.dumps(list(completion_sources.keys())),
                main_source['type']
            ))
            
            conn.commit()
        
        return {
            **completed_card,
            'is_complete': len(missing_fields) == 0 or len(completion_sources) > 0,
            'fallback_used': len(completion_sources) > 0,
            'completed_fields': list(completion_sources.keys()),
            'completion_sources': completion_sources
        }
        
    except Exception as e:
        print(f"Erreur lors de la complétion de la carte {card_id}: {e}")
        return original_card
        
    finally:
        cursor.close()
        conn.close()


def batch_complete_cards(set_code=None, limit=None):
    """
    Complète en lot les cartes d'un set ou toutes les cartes
    
    Args:
        set_code (str): Code du set à traiter (None = tous)
        limit (int): Limite du nombre de cartes à traiter
    
    Returns:
        dict: Statistiques de complétion
    """
    
    config = get_db_config()
    conn = mysql.connector.connect(**config)
    cursor = conn.cursor(dictionary=True)
    
    # Construire la requête
    where_clause = ""
    params = []
    
    if set_code:
        where_clause = "WHERE e.code = %s"
        params.append(set_code)
    
    limit_clause = ""
    if limit:
        limit_clause = f"LIMIT {limit}"
    
    cursor.execute(f"""
        SELECT c.id
        FROM pokemon_cards c
        JOIN expansions e ON c.expansion_id = e.id
        {where_clause}
        ORDER BY c.id
        {limit_clause}
    """, params)
    
    card_ids = [row['id'] for row in cursor.fetchall()]
    
    stats = {
        'total_processed': 0,
        'completed': 0,
        'already_complete': 0,
        'errors': 0
    }
    
    for card_id in card_ids:
        try:
            result = complete_card_data(card_id)
            stats['total_processed'] += 1
            
            if result and result.get('fallback_used'):
                stats['completed'] += 1
                print(f"✅ Carte {card_id} complétée: {result.get('completed_fields', [])}")
            else:
                stats['already_complete'] += 1
                
        except Exception as e:
            stats['errors'] += 1
            print(f"❌ Erreur carte {card_id}: {e}")
    
    cursor.close()
    conn.close()
    
    return stats
