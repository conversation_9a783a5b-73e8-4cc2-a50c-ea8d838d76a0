# 🚀 Démarrage Rapide - Pokemon TCG Scraper

## ⚡ EN 3 COMMANDES

```bash
# 1. Activer l'environnement virtuel
source .venv/bin/activate

# 2. Scraper (choisir une option)
python3 scraping_thai.py                    # TOUT scraper
python3 scraping_thai.py --expansion SV4a   # Scraper seulement SV4a
python3 launcher.py                         # Interface simple

# 3. Vérifier les résultats
python3 verify_scraping_results.py
```

**🎉 C'est tout ! Vos données et images sont prêtes !**

---

## 📊 VÉRIFICATIONS RAPIDES

```bash
# Test base de données
python3 -c "import mysql.connector; print('✅ MySQL OK')"

# Compter les expansions
ls images/ | wc -l          # Résultat attendu : 70+

# Compter les images SV4a
ls images/SV4a_TH/ | wc -l  # Résultat attendu : 311

# Taille totale
du -sh images/              # Résultat attendu : 6.8G
```

---

## 🎯 COMMANDES ESSENTIELLES

### Scraping
```bash
python3 scraping_thai.py                    # Scraper tout
python3 scraping_thai.py --expansion SV4a   # Scraper SV4a
python3 scraping_thai.py --verbose          # Mode détaillé
python3 launcher.py                         # Interface simple
```

### Vérification
```bash
python3 verify_scraping_results.py          # Vérifier tout
python3 verify_scraping_results.py --stats  # Statistiques
```

---

## 🚨 DÉPANNAGE EXPRESS

```bash
# Erreur MySQL
sudo systemctl start mariadb

# Environnement virtuel
source .venv/bin/activate

# Images manquantes
python3 scraping_thai.py --expansion SV4a
```

**🚀 Projet 100% opérationnel sur le disque dur externe !**
