#!/usr/bin/env python3
"""
Script pour normaliser les illustrateurs dans la base de données Pokemon TCG
- Extrait tous les noms d'illustrateurs des cartes
- Les insère dans la table pokemon_illustrators
- Met à jour pokemon_cards pour utiliser illustrator_id
"""

import mysql.connector
import sys
import os
import re
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def normalize_illustrator_name(name):
    """Normalise le nom d'un illustrateur"""
    if not name or name.strip() == '':
        return None
    
    # Nettoyer le nom
    name = name.strip()
    
    # Supprimer les caractères spéciaux en début/fin
    name = re.sub(r'^[^\w\s]+|[^\w\s]+$', '', name)
    
    # Normaliser les espaces
    name = re.sub(r'\s+', ' ', name)
    
    # Cas spéciaux courants
    replacements = {
        '<PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
        '<PERSON>': '<PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>': '<PERSON>',
        '<PERSON><PERSON><PERSON>': 'Atsuko <PERSON>shida',
        '<PERSON>shida Atsuko': 'Atsuko <PERSON>shida',
    }
    
    return replacements.get(name, name)

def extract_illustrators_from_cards():
    """Extrait tous les illustrateurs uniques des cartes"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("🎨 EXTRACTION DES ILLUSTRATEURS DEPUIS LES CARTES")
        print("=" * 60)
        
        # Chercher dans toutes les colonnes qui pourraient contenir des illustrateurs
        illustrator_sources = []
        
        # Vérifier quelles colonnes existent
        cursor.execute("DESCRIBE pokemon_cards")
        columns = cursor.fetchall()
        
        potential_columns = []
        for col in columns:
            col_name = col['Field']
            if any(keyword in col_name.lower() for keyword in ['illustrator', 'artist', 'creator']):
                potential_columns.append(col_name)
        
        print(f"📋 Colonnes potentielles trouvées: {potential_columns}")
        
        # Si aucune colonne illustrateur trouvée, chercher dans les données JSON ou autres
        if not potential_columns:
            print("⚠️  Aucune colonne illustrateur directe trouvée")
            print("🔍 Recherche dans les données existantes...")
            
            # Chercher dans les anciennes tables si elles existent encore
            cursor.execute("SHOW TABLES LIKE 'cards'")
            if cursor.fetchall():
                cursor.execute("DESCRIBE cards")
                old_columns = cursor.fetchall()
                for col in old_columns:
                    if 'illustrator' in col['Field'].lower():
                        print(f"   Trouvé dans ancienne table: cards.{col['Field']}")
                        
                        # Extraire les illustrateurs de l'ancienne table
                        cursor.execute(f"SELECT DISTINCT {col['Field']} FROM cards WHERE {col['Field']} IS NOT NULL AND {col['Field']} != ''")
                        old_illustrators = cursor.fetchall()
                        for ill in old_illustrators:
                            name = normalize_illustrator_name(ill[col['Field']])
                            if name:
                                illustrator_sources.append(name)
        
        # Extraire depuis les colonnes trouvées (sauf illustrator_id qui est numérique)
        for col_name in potential_columns:
            if col_name == 'illustrator_id':
                print(f"⚠️  Ignoré {col_name} (colonne numérique)")
                continue

            print(f"🔍 Extraction depuis {col_name}...")
            cursor.execute(f"SELECT DISTINCT {col_name} FROM pokemon_cards WHERE {col_name} IS NOT NULL AND {col_name} != ''")
            results = cursor.fetchall()

            for result in results:
                name = normalize_illustrator_name(result[col_name])
                if name and name not in illustrator_sources:
                    illustrator_sources.append(name)
        
        # Ajouter quelques illustrateurs connus du Pokemon TCG
        known_illustrators = [
            'Mitsuhiro Arita',
            'Ken Sugimori', 
            'Atsuko Nishida',
            'Kouki Saitou',
            'Kagemaru Himeno',
            'Yuka Morii',
            'Shin Nagasawa',
            'Naoki Saito',
            'Masakazu Fukuda',
            'Akira Komayama'
        ]
        
        for known in known_illustrators:
            if known not in illustrator_sources:
                illustrator_sources.append(known)
        
        print(f"✅ {len(illustrator_sources)} illustrateurs uniques trouvés")
        
        return list(set(illustrator_sources))  # Supprimer les doublons
        
    except Exception as e:
        print(f"❌ Erreur extraction: {e}")
        return []
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def populate_illustrators_table(illustrators):
    """Remplit la table pokemon_illustrators"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor()
        
        print(f"\n📝 INSERTION DE {len(illustrators)} ILLUSTRATEURS")
        print("=" * 60)
        
        inserted_count = 0
        
        for illustrator in illustrators:
            try:
                cursor.execute("""
                    INSERT INTO pokemon_illustrators (name, created_at)
                    VALUES (%s, NOW())
                    ON DUPLICATE KEY UPDATE name = VALUES(name)
                """, (illustrator,))
                
                if cursor.rowcount > 0:
                    inserted_count += 1
                    print(f"   ✅ {illustrator}")
                else:
                    print(f"   ⚠️  {illustrator} (déjà existant)")
                    
            except mysql.connector.Error as e:
                print(f"   ❌ Erreur {illustrator}: {e}")
                continue
        
        connection.commit()
        print(f"\n📊 {inserted_count} illustrateurs insérés")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur insertion: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def update_cards_with_illustrator_ids():
    """Met à jour les cartes pour utiliser illustrator_id"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print(f"\n🔄 MISE À JOUR DES CARTES AVEC ILLUSTRATOR_ID")
        print("=" * 60)
        
        # Créer un mapping nom -> id
        cursor.execute("SELECT id, name FROM pokemon_illustrators")
        illustrators_map = {ill['name']: ill['id'] for ill in cursor.fetchall()}
        
        print(f"📋 {len(illustrators_map)} illustrateurs disponibles")
        
        # Chercher les colonnes illustrateur dans pokemon_cards
        cursor.execute("DESCRIBE pokemon_cards")
        columns = cursor.fetchall()
        
        illustrator_columns = []
        for col in columns:
            if 'illustrator' in col['Field'].lower() and col['Field'] != 'illustrator_id':
                illustrator_columns.append(col['Field'])
        
        updated_count = 0
        
        # Mettre à jour depuis chaque colonne trouvée
        for col_name in illustrator_columns:
            print(f"🔍 Traitement de la colonne {col_name}...")
            
            cursor.execute(f"""
                SELECT id, {col_name} 
                FROM pokemon_cards 
                WHERE {col_name} IS NOT NULL 
                AND {col_name} != '' 
                AND illustrator_id IS NULL
            """)
            
            cards_to_update = cursor.fetchall()
            
            for card in cards_to_update:
                normalized_name = normalize_illustrator_name(card[col_name])
                if normalized_name and normalized_name in illustrators_map:
                    illustrator_id = illustrators_map[normalized_name]
                    
                    cursor.execute("""
                        UPDATE pokemon_cards 
                        SET illustrator_id = %s 
                        WHERE id = %s
                    """, (illustrator_id, card['id']))
                    
                    updated_count += 1
        
        connection.commit()
        print(f"\n📊 {updated_count} cartes mises à jour avec illustrator_id")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur mise à jour: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def verify_normalization():
    """Vérifie que la normalisation s'est bien passée"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print(f"\n🔍 VÉRIFICATION DE LA NORMALISATION")
        print("=" * 60)
        
        # Compter les illustrateurs
        cursor.execute("SELECT COUNT(*) as count FROM pokemon_illustrators")
        illustrator_count = cursor.fetchone()['count']
        print(f"📊 Illustrateurs dans pokemon_illustrators: {illustrator_count}")
        
        # Compter les cartes avec illustrator_id
        cursor.execute("SELECT COUNT(*) as count FROM pokemon_cards WHERE illustrator_id IS NOT NULL")
        cards_with_id = cursor.fetchone()['count']
        print(f"📊 Cartes avec illustrator_id: {cards_with_id}")
        
        # Top illustrateurs
        cursor.execute("""
            SELECT pi.name, COUNT(pc.id) as card_count
            FROM pokemon_illustrators pi
            LEFT JOIN pokemon_cards pc ON pi.id = pc.illustrator_id
            GROUP BY pi.id, pi.name
            ORDER BY card_count DESC
            LIMIT 5
        """)
        
        top_illustrators = cursor.fetchall()
        print(f"\n🏆 TOP 5 ILLUSTRATEURS:")
        for ill in top_illustrators:
            print(f"   {ill['name']}: {ill['card_count']} cartes")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur vérification: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    print("🎨 NORMALISATION DES ILLUSTRATEURS POKEMON TCG")
    print("=" * 50)
    
    # Demander confirmation
    response = input("\n⚠️  Voulez-vous normaliser les illustrateurs ? (oui/non): ")
    if response.lower() not in ['oui', 'o', 'yes', 'y']:
        print("❌ Normalisation annulée")
        sys.exit(0)
    
    # 1. Extraire les illustrateurs
    illustrators = extract_illustrators_from_cards()
    
    if not illustrators:
        print("❌ Aucun illustrateur trouvé")
        sys.exit(1)
    
    # 2. Remplir la table pokemon_illustrators
    if not populate_illustrators_table(illustrators):
        print("❌ Échec de l'insertion des illustrateurs")
        sys.exit(1)
    
    # 3. Mettre à jour les cartes
    if not update_cards_with_illustrator_ids():
        print("❌ Échec de la mise à jour des cartes")
        sys.exit(1)
    
    # 4. Vérifier
    verify_normalization()
    
    print("\n✅ Normalisation des illustrateurs terminée!")
