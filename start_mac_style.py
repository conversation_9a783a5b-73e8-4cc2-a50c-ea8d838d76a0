#!/usr/bin/env python3
"""
Pokemon TCG Scraper - Mac Style Launcher
Reproduit l'expérience de votre Mac en local
"""

import os
import sys
import subprocess

def clear_screen():
    os.system('clear' if os.name == 'posix' else 'cls')

def main():
    clear_screen()
    
    print("🃏 POKEMON TCG SCRAPER")
    print("=" * 50)
    print("🌐 Interface Web - Accessible sur le réseau")
    print("📍 Local: http://127.0.0.1:5051/")
    print("🌐 Réseau: http://192.168.2.240:5051/")
    print("=" * 50)
    print()
    print("1. 🌐 Lancer l'Interface Web")
    print("2. 🇯🇵 Scraper Bulbapedia")
    print("3. 🇹🇭 Scraper Thai")
    print("4. 🇮🇩 Scraper Indonésien")
    print("0. ❌ Quitter")
    print()
    
    choice = input("Choisissez une option: ")
    
    if choice == "1":
        print("\n🚀 Lancement de l'interface web...")
        print("📍 Local: http://127.0.0.1:5051/")
        print("🌐 Réseau: http://192.168.2.240:5051/")
        print("💡 Accessible depuis n'importe quel appareil sur votre réseau")
        print("💡 Appuyez sur Ctrl+C pour arrêter le serveur")
        print()
        
        # Changer vers le répertoire scraping
        os.chdir("scraping")
        
        # Configurer l'environnement
        env = os.environ.copy()
        env['PYTHONPATH'] = '/media/devmon/piHDD/Sites/pokemon-tcg-scraper'
        env['FLASK_DEBUG'] = 'true'
        
        # Supprimer l'environnement virtuel s'il est actif
        if 'VIRTUAL_ENV' in env:
            del env['VIRTUAL_ENV']
        
        try:
            # Lancer l'application Flask
            subprocess.run(['python3', 'app.py'], env=env)
        except KeyboardInterrupt:
            print("\n👋 Serveur arrêté")
        
    elif choice == "2":
        print("\n🇯🇵 Lancement du scraper Bulbapedia...")
        os.chdir("scraping")
        subprocess.run(['python3', 'scraping_bulbapedia.py'])
        
    elif choice == "3":
        print("\n🇹🇭 Lancement du scraper Thai...")
        os.chdir("scraping")
        subprocess.run(['python3', 'scraping_thai.py'])
        
    elif choice == "4":
        print("\n🇮🇩 Lancement du scraper Indonésien...")
        os.chdir("scraping")
        subprocess.run(['python3', 'scraping_indonesian.py'])
        
    elif choice == "0":
        print("\n👋 Au revoir!")
        sys.exit(0)
        
    else:
        print("\n❌ Option invalide")
        input("Appuyez sur Entrée pour continuer...")
        main()

if __name__ == "__main__":
    main()
