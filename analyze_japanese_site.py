#!/usr/bin/env python3
"""
Analyse complète du site japonais Pokémon TCG
pour comprendre la structure et créer un scraper complet
"""

import requests
from bs4 import BeautifulSoup
import re
import time
import json

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1'
}

def analyze_main_search_page():
    """Analyser la page de recherche principale"""
    print("🔍 ANALYSE PAGE PRINCIPALE")
    print("=" * 50)
    
    url = "https://www.pokemon-card.com/card-search/index.php"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 1. Analyser les filtres disponibles
            print("\n📋 FILTRES DISPONIBLES:")
            
            # Sélecteurs d'expansions
            expansion_selects = soup.find_all('select', {'name': re.compile(r'se_ta|expansion|set')})
            for select in expansion_selects:
                name = select.get('name', 'unknown')
                options = select.find_all('option')
                print(f"  - {name}: {len(options)} options")
                
                # Afficher quelques exemples
                for option in options[:5]:
                    value = option.get('value', '')
                    text = option.get_text(strip=True)
                    if value and text:
                        print(f"    * {value}: {text}")
            
            # 2. Analyser les paramètres de recherche
            print("\n🔍 PARAMÈTRES DE RECHERCHE:")
            forms = soup.find_all('form')
            for form in forms:
                inputs = form.find_all(['input', 'select'])
                for inp in inputs[:10]:  # Limiter l'affichage
                    name = inp.get('name', '')
                    input_type = inp.get('type', inp.name)
                    if name:
                        print(f"  - {name} ({input_type})")
            
            # 3. Chercher des patterns d'URLs
            print("\n🔗 PATTERNS D'URLS:")
            links = soup.find_all('a', href=True)
            url_patterns = set()
            for link in links:
                href = link.get('href', '')
                if 'card-search' in href or 'detail' in href:
                    # Extraire le pattern
                    pattern = re.sub(r'\d+', 'ID', href)
                    url_patterns.add(pattern)
            
            for pattern in sorted(url_patterns):
                print(f"  - {pattern}")
            
            return True
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_search_with_params():
    """Tester la recherche avec différents paramètres"""
    print("\n🧪 TEST RECHERCHE AVEC PARAMÈTRES")
    print("=" * 50)
    
    # Test 1: Recherche basique
    params = {
        'keyword': '',
        'se_ta': '',
        'regulation_sidebar_form': 'all',
        'pg': '1',
        'illust': '',
        'sm_and_keyword': 'true'
    }
    
    url = "https://www.pokemon-card.com/card-search/index.php"
    
    try:
        response = requests.get(url, params=params, headers=HEADERS, timeout=15)
        print(f"Status recherche: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Chercher les cartes dans les résultats
            card_elements = soup.find_all(['div', 'li', 'article'], class_=re.compile(r'card|item|result', re.I))
            print(f"Éléments de cartes trouvés: {len(card_elements)}")
            
            # Analyser la structure des cartes
            for i, card in enumerate(card_elements[:3]):
                print(f"\n📄 CARTE {i+1}:")
                
                # Chercher des liens
                links = card.find_all('a', href=True)
                for link in links:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    if href and ('detail' in href or 'card' in href):
                        print(f"  Lien: {href}")
                        print(f"  Texte: {text}")
                
                # Chercher des images
                images = card.find_all('img')
                for img in images:
                    src = img.get('src', '')
                    alt = img.get('alt', '')
                    if src:
                        print(f"  Image: {src}")
                        print(f"  Alt: {alt}")
            
            # Chercher la pagination
            pagination = soup.find_all(['div', 'nav'], class_=re.compile(r'pag|page', re.I))
            print(f"\nPagination trouvée: {len(pagination)} éléments")
            
            for pag in pagination:
                links = pag.find_all('a', href=True)
                print(f"  Liens pagination: {len(links)}")
                for link in links[:5]:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    if href:
                        print(f"    - {text}: {href}")
            
            return True
        else:
            print(f"❌ Erreur recherche: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur recherche: {e}")
        return False

def analyze_card_detail_structure():
    """Analyser la structure d'une page de détail de carte"""
    print("\n🔍 ANALYSE STRUCTURE DÉTAIL CARTE")
    print("=" * 50)
    
    # Essayer de trouver une URL de détail depuis la recherche
    search_url = "https://www.pokemon-card.com/card-search/index.php"
    
    try:
        response = requests.get(search_url, headers=HEADERS, timeout=15)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Chercher des liens vers des détails
            detail_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'detail' in href or 'card' in href:
                    detail_links.append(href)
            
            if detail_links:
                # Tester le premier lien de détail
                detail_url = detail_links[0]
                if not detail_url.startswith('http'):
                    detail_url = 'https://www.pokemon-card.com' + detail_url
                
                print(f"Test URL détail: {detail_url}")
                
                detail_response = requests.get(detail_url, headers=HEADERS, timeout=15)
                print(f"Status détail: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    detail_soup = BeautifulSoup(detail_response.content, 'html.parser')
                    
                    # Analyser la structure
                    print("\n📋 STRUCTURE PAGE DÉTAIL:")
                    
                    # Titre/nom
                    titles = detail_soup.find_all(['h1', 'h2', 'h3'])
                    for title in titles[:3]:
                        text = title.get_text(strip=True)
                        if text:
                            print(f"  Titre: {text}")
                    
                    # Images
                    images = detail_soup.find_all('img')
                    for img in images:
                        src = img.get('src', '')
                        alt = img.get('alt', '')
                        if 'card' in src.lower() or 'pokemon' in src.lower():
                            print(f"  Image carte: {src}")
                    
                    # Informations de carte
                    info_elements = detail_soup.find_all(['span', 'div', 'td'], 
                                                       class_=re.compile(r'number|rarity|type|hp', re.I))
                    for elem in info_elements:
                        classes = elem.get('class', [])
                        text = elem.get_text(strip=True)
                        if text and len(text) < 50:
                            print(f"  Info ({classes}): {text}")
                    
                    return True
            else:
                print("❌ Aucun lien de détail trouvé")
                return False
        else:
            print(f"❌ Erreur page recherche: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur analyse détail: {e}")
        return False

def detect_all_expansions():
    """Détecter toutes les expansions disponibles"""
    print("\n📦 DÉTECTION DE TOUTES LES EXPANSIONS")
    print("=" * 50)
    
    url = "https://www.pokemon-card.com/card-search/index.php"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Chercher tous les sélecteurs d'expansions
            expansion_data = []
            
            selects = soup.find_all('select')
            for select in selects:
                name = select.get('name', '')
                if any(keyword in name.lower() for keyword in ['se_ta', 'expansion', 'set', 'series']):
                    print(f"\n🎯 Sélecteur trouvé: {name}")
                    
                    options = select.find_all('option')
                    for option in options:
                        value = option.get('value', '').strip()
                        text = option.get_text(strip=True)
                        
                        if value and text and value != '':
                            expansion_data.append({
                                'selector': name,
                                'value': value,
                                'name': text
                            })
                            print(f"  ✅ {value}: {text}")
            
            print(f"\n📊 TOTAL EXPANSIONS DÉTECTÉES: {len(expansion_data)}")
            return expansion_data
        else:
            print(f"❌ Erreur: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Erreur détection expansions: {e}")
        return []

def main():
    print("🇯🇵 ANALYSE COMPLÈTE DU SITE JAPONAIS POKÉMON TCG")
    print("=" * 60)
    print("🎯 Objectif: Comprendre la structure pour créer un scraper complet")
    print("=" * 60)
    
    # 1. Analyser la page principale
    if analyze_main_search_page():
        print("✅ Analyse page principale réussie")
    else:
        print("❌ Échec analyse page principale")
    
    time.sleep(2)
    
    # 2. Tester la recherche
    if test_search_with_params():
        print("✅ Test recherche réussi")
    else:
        print("❌ Échec test recherche")
    
    time.sleep(2)
    
    # 3. Analyser structure détail
    if analyze_card_detail_structure():
        print("✅ Analyse détail réussie")
    else:
        print("❌ Échec analyse détail")
    
    time.sleep(2)
    
    # 4. Détecter toutes les expansions
    expansions = detect_all_expansions()
    if expansions:
        print("✅ Détection expansions réussie")
    else:
        print("❌ Échec détection expansions")
    
    print(f"\n🎉 ANALYSE TERMINÉE")

if __name__ == "__main__":
    main()
