-- Fix Web Interface Compatibility
-- Créer des vues pour que l'interface web fonctionne avec la nouvelle structure

USE pokemon_tcg_db;

-- Vue de compatibilité pour remplacer la table 'cards' manquante
-- L'interface web cherche une table 'cards' qui n'existe pas
CREATE OR REPLACE VIEW `cards` AS
SELECT 
    pc.id,
    pc.card_number,
    pc.expansion_id,
    pc.illustrator_id,
    pc.source,
    pc.linked_card_id,
    pc.created_at,
    pc.updated_at,
    -- Colonnes additionnelles que l'interface web pourrait chercher
    cv_en.name as name_en,
    cv_th.name as name_th,
    cv_id.name as name_id,
    cv_en.rarity,
    cv_en.image_url,
    cv_th.image_url as image_url_th,
    cv_id.image_url as image_url_id,
    cv_en.web_id,
    -- Colonnes legacy pour compatibilité
    pc.expansion_id as set_id,
    cv_en.name as name,
    '' as card_type,
    '' as hp,
    '' as retreat_cost,
    '' as weakness_type,
    '' as weakness_multiplier,
    '' as resistance_type,
    '' as resistance_value,
    '' as stage,
    '' as card_url,
    0 as national_pokedex_id
FROM pokemon_cards pc
LEFT JOIN card_versions cv_en ON pc.id = cv_en.card_id AND cv_en.language_id = (SELECT id FROM languages WHERE code = 'en')
LEFT JOIN card_versions cv_th ON pc.id = cv_th.card_id AND cv_th.language_id = (SELECT id FROM languages WHERE code = 'th')
LEFT JOIN card_versions cv_id ON pc.id = cv_id.card_id AND cv_id.language_id = (SELECT id FROM languages WHERE code = 'id');

-- Vue pour les statistiques unifiées
CREATE OR REPLACE VIEW `unified_sets_view` AS
SELECT 
    e.id,
    e.code,
    e.name_en,
    e.name_th,
    e.name_id,
    e.region,
    e.release_date,
    COUNT(DISTINCT pc.id) as total_cards,
    COUNT(DISTINCT cv_en.id) as cards_with_english,
    COUNT(DISTINCT cv_th.id) as cards_with_thai,
    COUNT(DISTINCT cv_id.id) as cards_with_indonesian,
    CASE 
        WHEN COUNT(DISTINCT cv_en.id) > 0 THEN 'English'
        WHEN COUNT(DISTINCT cv_th.id) > 0 THEN 'Thai'
        WHEN COUNT(DISTINCT cv_id.id) > 0 THEN 'Indonesian'
        ELSE 'Unknown'
    END as primary_language,
    e.created_at,
    e.updated_at
FROM expansions e
LEFT JOIN pokemon_cards pc ON e.id = pc.expansion_id
LEFT JOIN card_versions cv_en ON pc.id = cv_en.card_id AND cv_en.language_id = (SELECT id FROM languages WHERE code = 'en')
LEFT JOIN card_versions cv_th ON pc.id = cv_th.card_id AND cv_th.language_id = (SELECT id FROM languages WHERE code = 'th')
LEFT JOIN card_versions cv_id ON pc.id = cv_id.card_id AND cv_id.language_id = (SELECT id FROM languages WHERE code = 'id')
GROUP BY e.id, e.code, e.name_en, e.name_th, e.name_id, e.region, e.release_date, e.created_at, e.updated_at;

-- Vérifier que les vues ont été créées
SHOW TABLES;

-- Tester la vue cards
SELECT COUNT(*) as total_cards_in_view FROM cards;

-- Tester la vue unified_sets_view
SELECT COUNT(*) as total_sets_in_view FROM unified_sets_view;

SELECT 'Vues de compatibilité créées avec succès!' as status;
