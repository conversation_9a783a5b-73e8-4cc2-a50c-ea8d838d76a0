#!/usr/bin/env python3
"""
Analyse de la page produits japonaise pour détecter les expansions
"""

import requests
from bs4 import BeautifulSoup
import re
import time

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
    'Referer': 'https://www.pokemon-card.com/',
    'Connection': 'keep-alive'
}

def analyze_products_page():
    """Analyser la page produits pour trouver les expansions"""
    print("🔍 ANALYSE PAGE PRODUITS JAPONAISE")
    print("=" * 50)
    
    url = "https://www.pokemon-card.com/products/"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            print(f"Taille page: {len(response.content)} bytes")
            
            # 1. Chercher tous les liens vers des produits
            print("\n📦 LIENS PRODUITS TROUVÉS:")
            product_links = []
            
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                # Chercher des patterns de produits/expansions
                if any(pattern in href for pattern in ['/products/', '/expansion/', '/set/', '/series/']):
                    if href not in ['/products/', '/products'] and text:
                        product_links.append({
                            'url': href,
                            'text': text
                        })
                        print(f"  ✅ {href} - {text}")
            
            # 2. Chercher des patterns de codes d'expansion dans le texte
            print(f"\n🔍 PATTERNS D'EXPANSION DANS LE TEXTE:")
            page_text = soup.get_text()
            
            # Patterns japonais courants pour les sets Pokémon
            expansion_patterns = [
                r'\b(SV\d+[a-zA-Z]*)\b',  # SV1S, SV2D, etc.
                r'\b(S\d+[a-zA-Z]*)\b',   # S1H, S2D, etc.
                r'\b(SM\d+[a-zA-Z]*)\b',  # SM1+, SM2K, etc.
                r'\b(XY\d+[a-zA-Z]*)\b',  # XY1, XY2, etc.
            ]
            
            found_sets = set()
            for pattern in expansion_patterns:
                matches = re.findall(pattern, page_text)
                for match in matches:
                    if len(match) <= 10:  # Filtrer les codes trop longs
                        found_sets.add(match)
            
            for expansion_code in sorted(found_sets):
                print(f"  ✅ Code trouvé: {expansion_code}")
            
            # 3. Chercher des images de produits
            print(f"\n🖼️  IMAGES DE PRODUITS:")
            product_images = []
            
            for img in soup.find_all('img'):
                src = img.get('src', '')
                alt = img.get('alt', '')
                
                if any(keyword in src.lower() for keyword in ['product', 'expansion', 'set', 'pack']):
                    product_images.append({
                        'src': src,
                        'alt': alt
                    })
                    print(f"  🖼️  {src} - {alt}")
            
            # 4. Analyser la structure pour trouver des conteneurs de produits
            print(f"\n📋 STRUCTURE PRODUITS:")
            
            # Chercher des conteneurs avec des classes liées aux produits
            product_containers = soup.find_all(['div', 'section', 'article'], 
                                             class_=re.compile(r'product|expansion|set|card', re.I))
            
            print(f"Conteneurs produits trouvés: {len(product_containers)}")
            
            for i, container in enumerate(product_containers[:5]):
                classes = container.get('class', [])
                children = len(container.find_all())
                print(f"  Conteneur {i+1}: {classes} ({children} enfants)")
                
                # Chercher des liens dans ce conteneur
                links = container.find_all('a', href=True)
                for link in links[:2]:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    if href and text:
                        print(f"    Lien: {href} - {text}")
            
            return {
                'product_links': product_links,
                'expansion_codes': list(found_sets),
                'product_images': product_images
            }
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_specific_product_pages():
    """Tester des pages de produits spécifiques"""
    print("\n🧪 TEST PAGES PRODUITS SPÉCIFIQUES")
    print("=" * 50)
    
    # URLs potentielles basées sur les patterns courants
    test_urls = [
        "https://www.pokemon-card.com/products/sv/",
        "https://www.pokemon-card.com/products/sv1s/",
        "https://www.pokemon-card.com/products/sv2d/",
        "https://www.pokemon-card.com/expansion/",
        "https://www.pokemon-card.com/series/",
    ]
    
    for url in test_urls:
        print(f"\n🔍 Test: {url}")
        
        try:
            response = requests.get(url, headers=HEADERS, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Chercher des éléments caractéristiques
                title = soup.find('title')
                if title:
                    print(f"Titre: {title.get_text(strip=True)}")
                
                # Chercher des liens vers des cartes
                card_links = soup.find_all('a', href=re.compile(r'card|detail'))
                print(f"Liens cartes: {len(card_links)}")
                
                for link in card_links[:3]:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    print(f"  - {href} : {text}")
            
        except Exception as e:
            print(f"Erreur: {e}")

def analyze_card_search_structure():
    """Analyser la structure de recherche de cartes"""
    print("\n🔍 ANALYSE STRUCTURE RECHERCHE CARTES")
    print("=" * 50)
    
    # Tester différentes approches de recherche
    search_approaches = [
        {
            'url': 'https://www.pokemon-card.com/card-search/',
            'method': 'GET'
        },
        {
            'url': 'https://www.pokemon-card.com/card-search/index.php',
            'method': 'GET',
            'params': {'keyword': '', 'pg': '1'}
        }
    ]
    
    for approach in search_approaches:
        print(f"\n🧪 Test: {approach['url']}")
        
        try:
            if approach['method'] == 'GET':
                params = approach.get('params', {})
                response = requests.get(approach['url'], params=params, headers=HEADERS, timeout=15)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Chercher des formulaires de recherche
                forms = soup.find_all('form')
                print(f"Formulaires trouvés: {len(forms)}")
                
                for i, form in enumerate(forms):
                    action = form.get('action', '')
                    method = form.get('method', 'GET')
                    print(f"  Form {i+1}: {method} {action}")
                    
                    # Analyser les champs du formulaire
                    inputs = form.find_all(['input', 'select', 'textarea'])
                    for inp in inputs[:5]:
                        name = inp.get('name', '')
                        input_type = inp.get('type', inp.name)
                        if name:
                            print(f"    - {name} ({input_type})")
                
                # Chercher des résultats de cartes
                card_results = soup.find_all(['div', 'li'], class_=re.compile(r'card|result|item', re.I))
                print(f"Résultats cartes potentiels: {len(card_results)}")
                
                for result in card_results[:3]:
                    classes = result.get('class', [])
                    text = result.get_text(strip=True)[:100]
                    print(f"  - {classes}: {text}...")
            
        except Exception as e:
            print(f"Erreur: {e}")

def main():
    print("🇯🇵 ANALYSE COMPLÈTE SITE JAPONAIS - FOCUS PRODUITS")
    print("=" * 60)
    
    # 1. Analyser la page produits
    products_data = analyze_products_page()
    
    time.sleep(2)
    
    # 2. Tester des pages spécifiques
    test_specific_product_pages()
    
    time.sleep(2)
    
    # 3. Analyser la recherche de cartes
    analyze_card_search_structure()
    
    print(f"\n🎉 ANALYSE TERMINÉE")
    
    if products_data:
        print(f"\n📊 RÉSUMÉ:")
        print(f"  - Liens produits: {len(products_data['product_links'])}")
        print(f"  - Codes expansion: {len(products_data['expansion_codes'])}")
        print(f"  - Images produits: {len(products_data['product_images'])}")

if __name__ == "__main__":
    main()
