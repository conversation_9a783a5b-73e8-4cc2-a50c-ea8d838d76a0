#!/usr/bin/env python3
"""
Script pour créer la base de données et les tables nécessaires
"""

import mysql.connector
from mysql.connector import Error
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

def create_database_and_tables():
    """Créer la base de données et les tables"""
    
    # Configuration de connexion
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': int(os.getenv('DB_PORT', '10004')),
        'user': os.getenv('DB_USER', 'root'),
        'unix_socket': os.getenv('DB_UNIX_SOCKET', '/Applications/MAMP/tmp/mysql/mysql.sock'),
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    # Essayer sans mot de passe d'abord (skip-grant-tables)
    try:
        print("Tentative de connexion sans mot de passe...")
        connection = mysql.connector.connect(**config)
        print("✅ Connexion réussie sans mot de passe")
    except Error as e:
        print(f"❌ Échec sans mot de passe: {e}")
        # Essayer avec mot de passe
        try:
            print("Tentative de connexion avec mot de passe...")
            config['password'] = os.getenv('DB_PASSWORD', 'root')
            connection = mysql.connector.connect(**config)
            print("✅ Connexion réussie avec mot de passe")
        except Error as e:
            print(f"❌ Échec avec mot de passe: {e}")
            return False
    
    cursor = connection.cursor()
    
    try:
        # Créer la base de données si elle n'existe pas
        database_name = os.getenv('DB_DATABASE', 'pokemon_tcg_db')
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {database_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"✅ Base de données '{database_name}' créée ou existe déjà")
        
        # Utiliser la base de données
        cursor.execute(f"USE {database_name}")
        
        # Créer les tables
        tables = {
            'languages': """
                CREATE TABLE IF NOT EXISTS languages (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(5) UNIQUE NOT NULL,
                    name VARCHAR(50) NOT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,

            'expansions': """
                CREATE TABLE IF NOT EXISTS expansions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(20) UNIQUE NOT NULL,
                    name_en VARCHAR(255),
                    name_id VARCHAR(255),
                    release_date DATE,
                    region VARCHAR(50)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,

            'pokemon_illustrators': """
                CREATE TABLE IF NOT EXISTS pokemon_illustrators (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,

            'pokemon_cards': """
                CREATE TABLE IF NOT EXISTS pokemon_cards (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    expansion_id INT,
                    card_number VARCHAR(50),
                    illustrator_id INT,
                    source VARCHAR(255),
                    linked_card_id INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (expansion_id) REFERENCES expansions(id),
                    FOREIGN KEY (illustrator_id) REFERENCES pokemon_illustrators(id),
                    INDEX idx_expansion (expansion_id),
                    INDEX idx_card_number (card_number),
                    INDEX idx_source (source)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,

            'card_versions': """
                CREATE TABLE IF NOT EXISTS card_versions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    card_id INT NOT NULL,
                    language_id INT NOT NULL,
                    name VARCHAR(255),
                    rarity VARCHAR(50),
                    image_url TEXT,
                    web_id VARCHAR(100),
                    source VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (card_id) REFERENCES pokemon_cards(id) ON DELETE CASCADE,
                    FOREIGN KEY (language_id) REFERENCES languages(id),
                    INDEX idx_card_language (card_id, language_id),
                    INDEX idx_web_id (web_id),
                    INDEX idx_source (source)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """
        }
        
        # Créer chaque table
        for table_name, table_sql in tables.items():
            cursor.execute(table_sql)
            print(f"✅ Table '{table_name}' créée ou existe déjà")
        
        # Insérer les langues de base
        languages_data = [
            ('en', 'English'),
            ('th', 'Thai'),
            ('id', 'Indonesian'),
            ('ja', 'Japanese'),
            ('fr', 'French')
        ]
        
        for code, name in languages_data:
            cursor.execute(
                "INSERT IGNORE INTO languages (code, name) VALUES (%s, %s)",
                (code, name)
            )
        
        print("✅ Langues de base insérées")
        
        # Vérifier les tables créées
        cursor.execute("SHOW TABLES")
        tables_list = cursor.fetchall()
        print("\n📋 Tables dans la base de données:")
        for table in tables_list:
            print(f"  - {table[0]}")
        
        connection.commit()
        print("\n🎉 Base de données et tables créées avec succès!")
        return True
        
    except Error as e:
        print(f"❌ Erreur lors de la création: {e}")
        return False
    
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("🔌 Connexion fermée")

if __name__ == "__main__":
    print("🚀 Création de la base de données Pokemon TCG")
    print("=" * 50)
    success = create_database_and_tables()
    if success:
        print("\n✅ Processus terminé avec succès!")
    else:
        print("\n❌ Échec du processus!")