#!/usr/bin/env python3
"""
Pokemon TCG Scraper - Network Interface Launcher
Configured for network access on 192.168.2.240
"""

import os
import sys
import subprocess

def main():
    print("🌐 Pokemon TCG Scraper - Network Interface Launcher")
    print("=" * 60)
    
    # Project paths
    project_root = "/media/devmon/piHDD/Sites/pokemon-tcg-scraper"
    scraping_dir = os.path.join(project_root, "scraping")
    
    print(f"📂 Project root: {project_root}")
    print(f"📂 Scraping dir: {scraping_dir}")
    
    # Check if directories exist
    if not os.path.exists(project_root):
        print(f"❌ Project directory not found: {project_root}")
        return
        
    if not os.path.exists(scraping_dir):
        print(f"❌ Scraping directory not found: {scraping_dir}")
        return
    
    # Check if app.py exists
    app_file = os.path.join(scraping_dir, "app.py")
    if not os.path.exists(app_file):
        print(f"❌ App file not found: {app_file}")
        return
    
    print("✅ All files found")
    print()
    
    # Prepare environment
    env = os.environ.copy()
    env['PYTHONPATH'] = project_root
    env['FLASK_DEBUG'] = 'true'
    env['FLASK_PORT'] = '5051'
    
    # Remove virtual environment if present
    if 'VIRTUAL_ENV' in env:
        del env['VIRTUAL_ENV']
        print("🔧 Virtual environment disabled")
    
    print("🚀 Starting Flask application for network access...")
    print("📍 Local URL: http://127.0.0.1:5051/")
    print("🌐 Network URL: http://192.168.2.240:5051/")
    print("🔧 Debug mode: ON")
    print("⚡ Accessible from any device on your network!")
    print("=" * 60)
    print()
    print("💡 Access from your browser:")
    print("   • Local: http://127.0.0.1:5051/")
    print("   • Network: http://192.168.2.240:5051/")
    print()
    print("💡 Press Ctrl+C to stop the server")
    print()
    
    try:
        # Launch the Flask application
        subprocess.run([
            'python3', 'app.py'
        ], cwd=scraping_dir, env=env)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
