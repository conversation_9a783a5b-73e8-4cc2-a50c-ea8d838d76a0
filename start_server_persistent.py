#!/usr/bin/env python3
"""
Pokemon TCG Scraper - Serveur Persistant
Redémarre automatiquement en cas d'arrêt
"""

import os
import sys
import time
import subprocess

def start_server():
    """Lance le serveur Flask"""
    print("🚀 Démarrage du serveur Pokemon TCG Scraper...")
    
    # Changer vers le répertoire scraping
    os.chdir("scraping")
    
    # Configurer l'environnement
    env = os.environ.copy()
    env['PYTHONPATH'] = '/media/devmon/piHDD/Sites/pokemon-tcg-scraper'
    
    # Supprimer l'environnement virtuel s'il est actif
    if 'VIRTUAL_ENV' in env:
        del env['VIRTUAL_ENV']
    
    # Code Python pour lancer Flask
    flask_code = '''
import sys
sys.path.insert(0, '/media/devmon/piHDD/Sites/pokemon-tcg-scraper')
import os

print("🌐 Pokemon TCG Scraper - Network Server")
print("📍 Local URL: http://127.0.0.1:5051/")
print("🌐 Network URL: http://*************:5051/")
print("🔧 Listening on all interfaces (0.0.0.0)")
print("⚡ Ready for network access!")
print("=" * 60)

from app import app
app.run(host='0.0.0.0', port=5051, debug=False, use_reloader=False, threaded=True)
'''
    
    try:
        # Lancer le serveur
        process = subprocess.run(['python3', '-c', flask_code], env=env)
        return process.returncode
    except KeyboardInterrupt:
        print("\n👋 Serveur arrêté par l'utilisateur")
        return 0
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        return 1

def main():
    """Boucle principale avec redémarrage automatique"""
    print("🃏 POKEMON TCG SCRAPER - SERVEUR PERSISTANT")
    print("=" * 60)
    print("🔄 Redémarrage automatique activé")
    print("💡 Appuyez sur Ctrl+C pour arrêter définitivement")
    print("=" * 60)
    print()
    
    restart_count = 0
    
    while True:
        try:
            if restart_count > 0:
                print(f"\n🔄 Redémarrage #{restart_count}...")
                time.sleep(2)
            
            # Lancer le serveur
            exit_code = start_server()
            
            if exit_code == 0:
                # Arrêt normal (Ctrl+C)
                break
            else:
                # Erreur - redémarrer
                restart_count += 1
                print(f"\n⚠️  Serveur arrêté (code: {exit_code})")
                print(f"🔄 Redémarrage dans 5 secondes...")
                time.sleep(5)
                
        except KeyboardInterrupt:
            print("\n👋 Arrêt définitif du serveur")
            break
        except Exception as e:
            print(f"\n❌ Erreur critique: {e}")
            restart_count += 1
            time.sleep(5)

if __name__ == "__main__":
    main()
