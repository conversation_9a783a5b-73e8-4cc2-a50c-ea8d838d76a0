#!/usr/bin/env python3
"""Test avec une vraie carte indonésienne"""

import sys
import os
sys.path.append('.')

from scraping.scraping_indonesian import scrape_card_details

def test_real_card():
    """Test avec une vraie carte indonésienne"""
    
    print("🧪 TEST AVEC UNE VRAIE CARTE INDONÉSIENNE")
    print("=" * 50)
    
    # URL d'une carte indonésienne réelle
    test_url = "https://asia.pokemon-card.com/id/card-search/detail/15861/"
    
    print(f"🔍 Test avec: {test_url}")
    
    # Scraper les détails de la carte
    print("\n1️⃣ Scraping des détails de la carte:")
    try:
        card_data = scrape_card_details(test_url)
        if card_data:
            print(f"   ✅ Données récupérées:")
            print(f"      Nom: {card_data.get('nameCard', 'N/A')}")
            print(f"      Numéro: {card_data.get('numberCard', 'N/A')}")
            print(f"      Web ID: {card_data.get('webId', 'N/A')}")
            print(f"      Image URL: {card_data.get('imageUrl', 'N/A')}")
            print(f"      Rareté: {card_data.get('rarity', 'N/A')}")
            
            # Afficher toutes les clés disponibles
            print(f"\n   📋 Toutes les données disponibles:")
            for key, value in card_data.items():
                print(f"      {key}: {value}")
                
        else:
            print(f"   ❌ Échec du scraping")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🏁 Test terminé!")

if __name__ == "__main__":
    test_real_card()
