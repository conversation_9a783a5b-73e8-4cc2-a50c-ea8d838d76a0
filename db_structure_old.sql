-- Table for Pokemon Illustrators
CREATE TABLE IF NOT EXISTS `pokemon_illustrators` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Pokemon Expansions (Sets)
CREATE TABLE IF NOT EXISTS `pokemon_expansions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VA<PERSON><PERSON><PERSON>(255),
    `english_name` VA<PERSON>HA<PERSON>(255),
    `language` VARCHAR(10),
    `release_date` DATE,
    `total_cards` INT,
    `source` VARCHAR(255),
    `is_global_name` BOOLEAN DEFAULT FALSE,
    `name_en` VARCHAR(255),
    `name_fr` VA<PERSON><PERSON><PERSON>(255),
    `name_de` VA<PERSON>HA<PERSON>(255),
    `name_es` VA<PERSON>HA<PERSON>(255),
    `name_it` VA<PERSON><PERSON><PERSON>(255),
    `name_pt` VA<PERSON><PERSON><PERSON>(255),
    `name_th` VA<PERSON><PERSON><PERSON>(255),
    `name_ko` VA<PERSON>HA<PERSON>(255),
    `code_en` VARCHAR(50) UNIQUE, -- Assuming codes like 'S7R' are unique for a language
    `code_fr` VARCHAR(50) UNIQUE,
    `code_de` VARCHAR(50) UNIQUE,
    `code_es` VARCHAR(50) UNIQUE,
    `code_it` VARCHAR(50) UNIQUE,
    `code_pt` VARCHAR(50) UNIQUE,
    `code_th` VARCHAR(50) UNIQUE,
    `code_ko` VARCHAR(50) UNIQUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Pokemon Cards
CREATE TABLE IF NOT EXISTS `pokemon_cards` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `set_id` INT, -- This is the foreign key to pokemon_expansions.id
    `name_en` VARCHAR(255),
    -- `expansion_id` INT, -- Appears to be the same as set_id in the diagram, linking to pokemon_expansions
    `illustrator_id` INT,
    `image_url` VARCHAR(1024),
    `national_pokedex_id` INT,
    `card_url` VARCHAR(1024),
    `card_type` VARCHAR(100),
    `hp` VARCHAR(10), -- HP can be non-numeric e.g. "VMAX" or have symbols
    `retreat_cost` VARCHAR(50),
    `weakness_type` VARCHAR(50),
    `weakness_multiplier` VARCHAR(10),
    `resistance_type` VARCHAR(50),
    `resistance_value` VARCHAR(10),
    `stage` VARCHAR(100),
    `source` VARCHAR(255),
    `linked_card_id` INT, -- For evolutions or related cards
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`set_id`) REFERENCES `pokemon_expansions`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (`illustrator_id`) REFERENCES `pokemon_illustrators`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (`linked_card_id`) REFERENCES `pokemon_cards`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Card Names (Multilingual)
CREATE TABLE IF NOT EXISTS `card_names` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `card_id` INT NOT NULL,
    `lang` VARCHAR(10) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY `card_lang_unique` (`card_id`, `lang`),
    FOREIGN KEY (`card_id`) REFERENCES `pokemon_cards`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Pokemon Abilities
CREATE TABLE IF NOT EXISTS `pokemon_abilities` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `card_id` INT NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`card_id`) REFERENCES `pokemon_cards`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Pokemon Attacks
CREATE TABLE IF NOT EXISTS `pokemon_attacks` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `card_id` INT NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `damage` VARCHAR(50),
    `energy_cost` VARCHAR(255),
    `description` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`card_id`) REFERENCES `pokemon_cards`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Set Mappings (if this is a table and not just a view/config)
-- Based on the name "set_mappings" (plural), it's likely a table.
CREATE TABLE IF NOT EXISTS `set_mappings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `bulbapedia_code` VARCHAR(50), -- e.g., S7R
    `thai_code` VARCHAR(50),
    `common_name` VARCHAR(255), -- A user-friendly name for the mapping
    `release_date` DATE,
    `is_variant` BOOLEAN DEFAULT FALSE, -- If this mapping represents a variant set
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `bulbapedia_thai_unique` (`bulbapedia_code`, `thai_code`) -- Assuming this combination should be unique
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Note: `set_mapping_view` from the diagram is likely a SQL VIEW.
-- Creating views requires base tables to exist. Example structure:
-- CREATE VIEW `set_mapping_view` AS
-- SELECT
--     sm.id,
--     sm.bulbapedia_code,
--     pe_bulbapedia.name AS bulbapedia_name, -- Assuming a join to pokemon_expansions
--     sm.common_name,
--     sm.release_date,
--     pe_bulbapedia.id AS bulbapedia_id, -- Assuming this refers to pokemon_expansions.id
--     pe_thai.id AS thai_expansion_id -- Assuming this refers to pokemon_expansions.id
-- FROM `set_mappings` sm
-- LEFT JOIN `pokemon_expansions` pe_bulbapedia ON sm.bulbapedia_code = pe_bulbapedia.code_en -- Or appropriate code column
-- LEFT JOIN `pokemon_expansions` pe_thai ON sm.thai_code = pe_thai.code_th; -- Or appropriate code column