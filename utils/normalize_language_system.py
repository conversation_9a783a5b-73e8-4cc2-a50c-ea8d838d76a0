#!/usr/bin/env python3
"""
Normalise le système de langues pour tous les scrapers
Crée une structure cohérente avec card_versions
"""

import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def create_normalized_structure():
    """Crée la structure normalisée avec card_versions"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor()
        
        print("🏗️ CRÉATION DE LA STRUCTURE NORMALISÉE:")
        print("=" * 60)
        
        # 1. Vérifier/créer la table languages
        print("📋 1. Table languages...")
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS languages (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(10) NOT NULL UNIQUE,
                    name VARCHAR(100) NOT NULL,
                    iso_code VARCHAR(5),
                    country_code VARCHAR(5),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
        except mysql.connector.Error as e:
            if "already exists" in str(e):
                print("   ✅ Table languages existe déjà")
            else:
                raise e
        
        # 2. Insérer les langues de base (structure simple)
        languages = [
            ('th', 'ไทย (Thai)'),
            ('id', 'Bahasa Indonesia'),
            ('en', 'English'),
            ('jp', '日本語 (Japanese)'),
            ('kr', '한국어 (Korean)')
        ]

        for code, name in languages:
            cursor.execute("""
                INSERT INTO languages (code, name)
                VALUES (%s, %s)
                ON DUPLICATE KEY UPDATE
                    name = VALUES(name)
            """, (code, name))
        
        print("   ✅ Langues initialisées")
        
        # 3. Créer la table card_versions
        print("📋 2. Table card_versions...")
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS card_versions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    card_id INT NOT NULL,
                    language_id INT NOT NULL,
                    name VARCHAR(255),
                    image_url TEXT,
                    web_id INT,
                    source VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_card_language (card_id, language_id),
                    INDEX idx_card_id (card_id),
                    INDEX idx_language_id (language_id),
                    INDEX idx_source (source)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
        except mysql.connector.Error as e:
            if "already exists" in str(e):
                print("   ✅ Table card_versions existe déjà")
            else:
                print(f"   ⚠️ Erreur card_versions: {e}")
        
        print("   ✅ Table card_versions créée")
        
        # 4. Modifier pokemon_cards pour enlever les colonnes spécifiques aux langues
        print("📋 3. Nettoyage pokemon_cards...")
        
        # Vérifier quelles colonnes existent
        cursor.execute("DESCRIBE pokemon_cards")
        columns = [row[0] for row in cursor.fetchall()]
        
        # Colonnes à garder (sans langue spécifique)
        base_columns = [
            'id', 'expansion_id', 'card_number', 'illustrator_id', 
            'linked_card_id', 'source', 'created_at', 'updated_at',
            'card_type', 'hp', 'rarity'
        ]
        
        # Colonnes à supprimer (spécifiques aux langues)
        language_columns = [
            'name_th', 'name_id', 'name_en', 'name_jp', 'name_kr',
            'image_url_th', 'image_url_id', 'image_url', 'image_url_jp',
            'web_id', 'card_url'
        ]
        
        # Supprimer les colonnes de langue (si elles existent)
        for col in language_columns:
            if col in columns:
                try:
                    cursor.execute(f"ALTER TABLE pokemon_cards DROP COLUMN {col}")
                    print(f"   🗑️ Colonne {col} supprimée")
                except mysql.connector.Error as e:
                    if "doesn't exist" not in str(e):
                        print(f"   ⚠️ Erreur suppression {col}: {e}")
        
        print("   ✅ pokemon_cards nettoyée")
        
        connection.commit()
        
        print("\n🎉 STRUCTURE NORMALISÉE CRÉÉE!")
        print("✅ Table languages avec 5 langues")
        print("✅ Table card_versions pour les versions linguistiques")
        print("✅ Table pokemon_cards nettoyée (cartes de base)")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ Erreur base de données: {e}")
        return False
    
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def migrate_existing_data():
    """Migre les données existantes vers la nouvelle structure"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("\n🔄 MIGRATION DES DONNÉES EXISTANTES:")
        print("=" * 60)
        
        # Récupérer les IDs des langues
        cursor.execute("SELECT id, code FROM languages")
        languages = {row['code']: row['id'] for row in cursor.fetchall()}
        
        print(f"📋 Langues disponibles: {list(languages.keys())}")
        
        # Compter les cartes à migrer
        cursor.execute("SELECT COUNT(*) as count FROM pokemon_cards")
        total_cards = cursor.fetchone()['count']
        
        if total_cards == 0:
            print("📊 Aucune carte à migrer (base vide)")
            return True
        
        print(f"📊 {total_cards} cartes à analyser pour migration")
        
        # Note: La migration sera faite par les nouveaux scrapers
        # qui utiliseront directement la nouvelle structure
        
        print("✅ Structure prête pour les nouveaux scrapers")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur migration: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def show_new_structure():
    """Affiche la nouvelle structure"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("\n📊 NOUVELLE STRUCTURE:")
        print("=" * 60)
        
        # Langues
        cursor.execute("SELECT * FROM languages ORDER BY code")
        languages = cursor.fetchall()
        print("🌍 LANGUES SUPPORTÉES:")
        for lang in languages:
            print(f"   {lang['code']}: {lang['name']}")
        
        # Tables
        print(f"\n📋 TABLES:")
        print(f"   pokemon_cards: Cartes de base (sans langue)")
        print(f"   card_versions: Versions linguistiques")
        print(f"   languages: Langues supportées")
        
        # Comptes
        cursor.execute("SELECT COUNT(*) as count FROM pokemon_cards")
        cards_count = cursor.fetchone()['count']
        
        cursor.execute("SELECT COUNT(*) as count FROM card_versions")
        versions_count = cursor.fetchone()['count']
        
        print(f"\n📊 DONNÉES:")
        print(f"   Cartes de base: {cards_count}")
        print(f"   Versions linguistiques: {versions_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur affichage: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    print("🌍 NORMALISATION DU SYSTÈME DE LANGUES")
    print("=" * 60)
    
    # 1. Créer la structure
    if not create_normalized_structure():
        print("❌ Échec création structure")
        sys.exit(1)
    
    # 2. Migrer les données
    if not migrate_existing_data():
        print("❌ Échec migration")
        sys.exit(1)
    
    # 3. Afficher le résultat
    show_new_structure()
    
    print("\n🎉 SYSTÈME DE LANGUES NORMALISÉ!")
    print("✅ Tous les scrapers peuvent maintenant utiliser la même structure")
    print("✅ Prêt pour un scraping cohérent")
