#!/usr/bin/env python3
"""
Télécharge les images manquantes pour les cartes existantes
"""

import mysql.connector
import requests
import os
import sys
import time
import logging
from urllib.parse import urljoin

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def download_image(image_url, local_path):
    """Télécharge une image depuis une URL"""
    try:
        # Créer le dossier si nécessaire
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(image_url, timeout=15, headers=headers)
        response.raise_for_status()
        
        with open(local_path, 'wb') as f:
            f.write(response.content)
        
        logging.info(f"Image téléchargée: {local_path}")
        return True
        
    except Exception as e:
        logging.error(f"Erreur téléchargement {image_url}: {e}")
        return False

def generate_image_filename(card_details, set_code):
    """Génère le nom de fichier pour l'image"""
    card_number = card_details.get('card_number', '000')
    web_id = card_details.get('web_id', '0000')
    
    # Nettoyer le numéro de carte
    clean_number = card_number.replace('/', '_').replace(' ', '_')
    
    return f"{clean_number}_{web_id}.jpg"

def get_card_details_from_web(web_id):
    """Récupère les détails d'une carte depuis le web"""
    try:
        url = f"https://asia.pokemon-card.com/th/card-search/details/{web_id}/"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, timeout=15, headers=headers)
        response.raise_for_status()
        
        # Parser basique pour extraire l'URL de l'image
        content = response.text
        
        # Chercher l'URL de l'image
        import re
        image_match = re.search(r'<img[^>]+src="([^"]+)"[^>]*class="[^"]*card-image', content)
        if image_match:
            image_url = image_match.group(1)
            if not image_url.startswith('http'):
                image_url = urljoin("https://asia.pokemon-card.com", image_url)
            return {'image_url': image_url}
        
        return None
        
    except Exception as e:
        logging.error(f"Erreur récupération détails carte {web_id}: {e}")
        return None

def download_missing_images():
    """Télécharge les images manquantes pour toutes les cartes thaï"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("🖼️ TÉLÉCHARGEMENT DES IMAGES MANQUANTES")
        print("=" * 60)
        
        # Récupérer toutes les cartes thaï avec leurs détails
        cursor.execute("""
            SELECT pc.id, pc.card_number, cv.web_id, cv.name, e.code as set_code
            FROM pokemon_cards pc
            JOIN card_versions cv ON pc.id = cv.card_id
            JOIN expansions e ON pc.expansion_id = e.id
            JOIN languages l ON cv.language_id = l.id
            WHERE l.code = 'th' AND pc.source = 'thai'
            ORDER BY pc.id
        """)
        
        cards = cursor.fetchall()
        
        print(f"📊 {len(cards)} cartes thaï trouvées")
        
        base_dir = "/media/devmon/piHDD/Sites/pokemon-tcg-scraper"
        downloaded_count = 0
        skipped_count = 0
        error_count = 0
        
        for i, card in enumerate(cards, 1):
            try:
                # Générer le chemin de l'image
                set_code = card['set_code']
                card_details = {
                    'card_number': card['card_number'],
                    'web_id': card['web_id']
                }
                
                image_filename = generate_image_filename(card_details, set_code)
                local_path = f"{base_dir}/images/{set_code}_TH/{image_filename}"
                
                # Vérifier si l'image existe déjà
                if os.path.exists(local_path):
                    if i % 50 == 0:  # Afficher le progrès tous les 50
                        print(f"   📊 Progression: {i}/{len(cards)} - {card['name'][:30]}... (existe)")
                    skipped_count += 1
                    continue
                
                print(f"📥 {i}/{len(cards)}: Téléchargement {card['name'][:30]}...")
                
                # Récupérer les détails depuis le web
                web_details = get_card_details_from_web(card['web_id'])
                
                if web_details and web_details.get('image_url'):
                    if download_image(web_details['image_url'], local_path):
                        downloaded_count += 1
                        print(f"   ✅ Image téléchargée: {image_filename}")
                    else:
                        error_count += 1
                        print(f"   ❌ Échec téléchargement: {image_filename}")
                else:
                    error_count += 1
                    print(f"   ❌ URL image non trouvée pour carte {card['web_id']}")
                
                # Pause pour éviter de surcharger le serveur
                time.sleep(1)
                
            except Exception as e:
                error_count += 1
                print(f"   ❌ Erreur carte {card['web_id']}: {e}")
                continue
        
        print(f"\n📊 RÉSUMÉ:")
        print(f"   ✅ Images téléchargées: {downloaded_count}")
        print(f"   ⚠️  Images existantes: {skipped_count}")
        print(f"   ❌ Erreurs: {error_count}")
        print(f"   📊 Total traité: {len(cards)}")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ Erreur base de données: {e}")
        return False
    
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    print("🖼️ TÉLÉCHARGEUR D'IMAGES MANQUANTES")
    print("=" * 60)
    
    if download_missing_images():
        print("✅ Téléchargement terminé avec succès!")
    else:
        print("❌ Échec du téléchargement")
        sys.exit(1)
