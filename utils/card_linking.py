#!/usr/bin/env python3
"""
Système de liaison des cartes multilingues
Utilise les correspondances de sets pour lier les cartes identiques
"""

import mysql.connector
import re
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def create_card_links_table():
    """Crée la table de liens entre cartes (si nécessaire)"""

    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor()

        # Vérifier si la table existe déjà
        cursor.execute("SHOW TABLES LIKE 'card_links'")
        if cursor.fetchall():
            print("✅ Table card_links existe déjà")
            return True

        cursor.execute("""
            CREATE TABLE card_links (
                id INT AUTO_INCREMENT PRIMARY KEY,
                master_card_id INT,
                english_card_id INT,
                japanese_card_id INT,
                thai_card_id INT,
                indonesian_card_id INT,
                korean_card_id INT,
                chinese_traditional_card_id INT,
                chinese_simplified_card_id INT,
                bulbapedia_card_id INT,
                card_number VARCHAR(20),
                card_name_en VARCHAR(255),
                set_mapping_id INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_master_card (master_card_id),
                INDEX idx_card_number (card_number),
                INDEX idx_set_mapping (set_mapping_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        connection.commit()
        print("✅ Table card_links créée")

        return True

    except mysql.connector.Error as e:
        print(f"❌ Erreur création table: {e}")
        return False

    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def normalize_card_number(card_number):
    """Normalise un numéro de carte pour la comparaison"""
    if not card_number:
        return None
    
    # Supprimer les espaces et convertir en minuscules
    normalized = re.sub(r'\s+', '', str(card_number)).lower()
    
    # Extraire le pattern numéro/total (ex: "001/100")
    match = re.search(r'(\d+)/(\d+)', normalized)
    if match:
        card_num = int(match.group(1))
        total_cards = int(match.group(2))
        return f"{card_num:03d}/{total_cards:03d}"
    
    # Extraire juste le numéro si pas de total
    match = re.search(r'(\d+)', normalized)
    if match:
        return f"{int(match.group(1)):03d}"
    
    return normalized

def normalize_card_name(name):
    """Normalise un nom de carte pour la comparaison"""
    if not name:
        return None
    
    # Supprimer les caractères spéciaux et normaliser
    normalized = re.sub(r'[^\w\s]', '', name.lower())
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    
    return normalized

def find_card_matches():
    """Trouve les correspondances entre cartes de différentes langues"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("🔍 Recherche des correspondances de cartes...")
        
        # Récupérer toutes les cartes avec leurs expansions
        cursor.execute("""
            SELECT
                pc.id,
                pc.card_number,
                pc.name_en,
                pc.name_th,
                pc.name_id,
                pc.source,
                pc.expansion_id,
                e.code as expansion_code,
                e.name_en as expansion_name,
                sm.id as set_mapping_id,
                sm.common_name
            FROM pokemon_cards pc
            JOIN expansions e ON pc.expansion_id = e.id
            LEFT JOIN set_mappings sm ON (
                sm.thai_code = e.code OR
                sm.bulbapedia_code = e.code
            )
            WHERE pc.card_number IS NOT NULL
            ORDER BY pc.card_number, pc.source
        """)
        
        all_cards = cursor.fetchall()
        
        # Grouper les cartes par numéro normalisé et set
        card_groups = {}
        
        for card in all_cards:
            normalized_number = normalize_card_number(card['card_number'])
            set_mapping_id = card['set_mapping_id']
            
            if normalized_number and set_mapping_id:
                key = f"{set_mapping_id}_{normalized_number}"
                
                if key not in card_groups:
                    card_groups[key] = []
                
                card_groups[key].append(card)
        
        # Créer les liens pour les groupes avec plusieurs cartes
        links_created = 0
        
        for group_key, cards in card_groups.items():
            if len(cards) > 1:
                # Créer un lien pour ce groupe
                link_id = create_card_link(cursor, cards)
                if link_id:
                    links_created += 1
                    print(f"   🔗 Lien créé: {group_key} ({len(cards)} cartes)")
        
        connection.commit()
        print(f"✅ {links_created} liens de cartes créés")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur recherche correspondances: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def create_card_link(cursor, cards):
    """Crée un lien entre plusieurs cartes"""
    
    try:
        # Organiser les cartes par source
        card_by_source = {}
        master_card = None
        card_number = None
        card_name_en = None
        set_mapping_id = None
        
        for card in cards:
            source = card['source']
            card_by_source[source] = card['id']
            
            # Utiliser la première carte comme référence
            if not master_card:
                master_card = card['id']
                card_number = card['card_number']
                card_name_en = card['name_en']
                set_mapping_id = card['set_mapping_id']
        
        # Insérer le lien
        cursor.execute("""
            INSERT INTO card_links (
                master_card_id,
                english_card_id,
                thai_card_id,
                indonesian_card_id,
                bulbapedia_card_id,
                card_number,
                card_name_en,
                set_mapping_id
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            master_card,
            card_by_source.get('english'),
            card_by_source.get('thai'),
            card_by_source.get('indonesian'),
            card_by_source.get('bulbapedia'),
            card_number,
            card_name_en,
            set_mapping_id
        ))
        
        link_id = cursor.lastrowid
        
        # Mettre à jour les cartes avec le linked_card_id
        for card in cards:
            cursor.execute("""
                UPDATE pokemon_cards 
                SET linked_card_id = %s 
                WHERE id = %s
            """, (link_id, card['id']))
        
        return link_id
        
    except mysql.connector.Error as e:
        print(f"❌ Erreur création lien: {e}")
        return None

def get_card_versions(card_id):
    """Récupère toutes les versions d'une carte"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        # Trouver le lien de cette carte
        cursor.execute("""
            SELECT linked_card_id FROM pokemon_cards WHERE id = %s
        """, (card_id,))
        
        result = cursor.fetchone()
        if not result or not result['linked_card_id']:
            return []
        
        link_id = result['linked_card_id']
        
        # Récupérer toutes les versions liées
        cursor.execute("""
            SELECT 
                cl.*,
                pc_en.name_en as english_name,
                pc_th.name_th as thai_name,
                pc_id.name_id as indonesian_name,
                pc_en.image_url as english_image,
                pc_th.image_url_th as thai_image,
                pc_id.image_url_id as indonesian_image
            FROM card_links cl
            LEFT JOIN pokemon_cards pc_en ON cl.english_card_id = pc_en.id
            LEFT JOIN pokemon_cards pc_th ON cl.thai_card_id = pc_th.id
            LEFT JOIN pokemon_cards pc_id ON cl.indonesian_card_id = pc_id.id
            WHERE cl.id = %s
        """, (link_id,))
        
        return cursor.fetchone()
        
    except Exception as e:
        print(f"❌ Erreur récupération versions: {e}")
        return None
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def update_linked_card_id_column():
    """Met à jour la colonne linked_card_id dans pokemon_cards"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor()
        
        # Vérifier si la colonne existe
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'pokemon_cards' 
            AND COLUMN_NAME = 'linked_card_id'
        """)
        
        if cursor.fetchone()[0] == 0:
            # Ajouter la colonne
            cursor.execute("""
                ALTER TABLE pokemon_cards 
                ADD COLUMN linked_card_id INT,
                ADD INDEX idx_linked_card (linked_card_id)
            """)
            
            connection.commit()
            print("✅ Colonne linked_card_id ajoutée")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ Erreur mise à jour colonne: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def show_linking_stats():
    """Affiche les statistiques de liaison"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("\n📊 STATISTIQUES DE LIAISON:")
        print("=" * 50)
        
        # Nombre total de liens
        cursor.execute("SELECT COUNT(*) as count FROM card_links")
        total_links = cursor.fetchone()['count']
        print(f"🔗 Total liens créés: {total_links}")
        
        # Cartes liées par source
        cursor.execute("""
            SELECT 
                pc.source,
                COUNT(DISTINCT pc.id) as total_cards,
                COUNT(DISTINCT pc.linked_card_id) as linked_cards
            FROM pokemon_cards pc
            WHERE pc.source IS NOT NULL
            GROUP BY pc.source
        """)
        
        source_stats = cursor.fetchall()
        print("\n📋 Cartes liées par source:")
        for stat in source_stats:
            linked_pct = (stat['linked_cards'] / stat['total_cards'] * 100) if stat['total_cards'] > 0 else 0
            print(f"   {stat['source']}: {stat['linked_cards']}/{stat['total_cards']} ({linked_pct:.1f}%)")
        
        # Top 10 sets avec le plus de liens
        cursor.execute("""
            SELECT 
                sm.bulbapedia_name,
                COUNT(cl.id) as link_count
            FROM card_links cl
            JOIN set_mappings sm ON cl.set_mapping_id = sm.id
            GROUP BY sm.id, sm.bulbapedia_name
            ORDER BY link_count DESC
            LIMIT 10
        """)
        
        top_sets = cursor.fetchall()
        if top_sets:
            print("\n🏆 Top 10 sets avec le plus de liens:")
            for i, set_stat in enumerate(top_sets, 1):
                print(f"   {i:2d}. {set_stat['bulbapedia_name']}: {set_stat['link_count']} liens")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur statistiques: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    print("🔗 SYSTÈME DE LIAISON DES CARTES MULTILINGUES")
    print("=" * 60)
    
    # 1. Créer les tables
    if not create_card_links_table():
        print("❌ Échec création table card_links")
        sys.exit(1)
    
    # 2. Mettre à jour la colonne linked_card_id
    if not update_linked_card_id_column():
        print("❌ Échec mise à jour colonne")
        sys.exit(1)
    
    # 3. Trouver les correspondances
    if not find_card_matches():
        print("❌ Échec recherche correspondances")
        sys.exit(1)
    
    # 4. Afficher les statistiques
    show_linking_stats()
    
    print("\n🎉 SYSTÈME DE LIAISON CRÉÉ AVEC SUCCÈS!")
    print("✅ Tables créées")
    print("✅ Correspondances trouvées")
    print("✅ Cartes liées")
