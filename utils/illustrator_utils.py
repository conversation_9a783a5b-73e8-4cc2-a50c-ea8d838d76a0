#!/usr/bin/env python3
"""
Utilitaires pour la gestion normalisée des illustrateurs Pokemon TCG
"""

import mysql.connector
import re
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def normalize_illustrator_name(name):
    """Normalise le nom d'un illustrateur"""
    if not name or name.strip() == '':
        return None
    
    # Nettoyer le nom
    name = name.strip()
    
    # Supprimer les caractères spéciaux en début/fin
    name = re.sub(r'^[^\w\s]+|[^\w\s]+$', '', name)
    
    # Normaliser les espaces
    name = re.sub(r'\s+', ' ', name)
    
    # Cas spéciaux courants (ordre prénom nom)
    replacements = {
        '<PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>': '<PERSON>',
        '<PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>': '<PERSON><PERSON>',
        '<PERSON><PERSON>': '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>': '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>': '<PERSON> <PERSON><PERSON>awa',
        '<PERSON><PERSON> <PERSON><PERSON>': '<PERSON><PERSON> <PERSON>to',
        '<PERSON><PERSON>da <PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON> <PERSON>kuda',
        '<PERSON>mayama <PERSON>': '<PERSON> <PERSON>mayama',
    }
    
    return replacements.get(name, name)

def get_or_create_illustrator(connection, illustrator_name):
    """<PERSON>cup<PERSON> ou crée un illustrateur et retourne son ID"""
    
    if not illustrator_name:
        return None
    
    # Normaliser le nom
    normalized_name = normalize_illustrator_name(illustrator_name)
    if not normalized_name:
        return None
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Chercher l'illustrateur existant
        cursor.execute("""
            SELECT id FROM pokemon_illustrators 
            WHERE name = %s
        """, (normalized_name,))
        
        existing = cursor.fetchone()
        if existing:
            return existing['id']
        
        # Créer un nouvel illustrateur
        cursor.execute("""
            INSERT INTO pokemon_illustrators (name, created_at)
            VALUES (%s, NOW())
        """, (normalized_name,))
        
        connection.commit()
        illustrator_id = cursor.lastrowid
        
        print(f"      🎨 Nouvel illustrateur créé: {normalized_name} (ID: {illustrator_id})")
        return illustrator_id
        
    except mysql.connector.Error as e:
        print(f"      ❌ Erreur illustrateur {normalized_name}: {e}")
        return None

def update_card_illustrator(connection, card_id, illustrator_name):
    """Met à jour l'illustrateur d'une carte existante"""
    
    if not illustrator_name:
        return False
    
    try:
        # Récupérer ou créer l'illustrateur
        illustrator_id = get_or_create_illustrator(connection, illustrator_name)
        if not illustrator_id:
            return False
        
        cursor = connection.cursor()
        
        # Mettre à jour la carte
        cursor.execute("""
            UPDATE pokemon_cards 
            SET illustrator_id = %s 
            WHERE id = %s
        """, (illustrator_id, card_id))
        
        connection.commit()
        
        if cursor.rowcount > 0:
            print(f"      🎨 Illustrateur mis à jour pour carte {card_id}: {illustrator_name}")
            return True
        
        return False
        
    except mysql.connector.Error as e:
        print(f"      ❌ Erreur mise à jour illustrateur: {e}")
        return False

def get_illustrator_stats():
    """Affiche les statistiques des illustrateurs"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("\n🎨 STATISTIQUES DES ILLUSTRATEURS:")
        print("=" * 50)
        
        # Nombre total d'illustrateurs
        cursor.execute("SELECT COUNT(*) as count FROM pokemon_illustrators")
        total_illustrators = cursor.fetchone()['count']
        print(f"📊 Total illustrateurs: {total_illustrators}")
        
        # Nombre de cartes avec illustrateur
        cursor.execute("SELECT COUNT(*) as count FROM pokemon_cards WHERE illustrator_id IS NOT NULL")
        cards_with_illustrator = cursor.fetchone()['count']
        print(f"📊 Cartes avec illustrateur: {cards_with_illustrator}")
        
        # Top 10 illustrateurs
        cursor.execute("""
            SELECT pi.name, COUNT(pc.id) as card_count
            FROM pokemon_illustrators pi
            LEFT JOIN pokemon_cards pc ON pi.id = pc.illustrator_id
            GROUP BY pi.id, pi.name
            HAVING card_count > 0
            ORDER BY card_count DESC
            LIMIT 10
        """)
        
        top_illustrators = cursor.fetchall()
        if top_illustrators:
            print(f"\n🏆 TOP 10 ILLUSTRATEURS:")
            for i, ill in enumerate(top_illustrators, 1):
                print(f"   {i:2d}. {ill['name']}: {ill['card_count']} cartes")
        else:
            print("\n⚠️  Aucune carte avec illustrateur trouvée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur statistiques: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def migrate_illustrators_from_old_data():
    """Migre les illustrateurs depuis les anciennes données"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("\n🔄 MIGRATION DES ILLUSTRATEURS DEPUIS ANCIENNES DONNÉES:")
        print("=" * 60)
        
        migrated_count = 0
        
        # Chercher dans l'ancienne table cards si elle existe
        cursor.execute("SHOW TABLES LIKE 'cards'")
        if cursor.fetchall():
            print("📋 Migration depuis table 'cards'...")
            
            cursor.execute("""
                SELECT DISTINCT illustrator 
                FROM cards 
                WHERE illustrator IS NOT NULL 
                AND illustrator != ''
            """)
            
            old_illustrators = cursor.fetchall()
            
            for ill_data in old_illustrators:
                illustrator_name = ill_data['illustrator']
                illustrator_id = get_or_create_illustrator(connection, illustrator_name)
                if illustrator_id:
                    migrated_count += 1
        
        print(f"✅ {migrated_count} illustrateurs migrés depuis les anciennes données")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur migration: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    print("🎨 UTILITAIRES ILLUSTRATEURS POKEMON TCG")
    print("=" * 40)
    
    # Afficher les statistiques
    get_illustrator_stats()
    
    # Proposer la migration
    print("\n" + "=" * 40)
    response = input("Voulez-vous migrer les illustrateurs depuis les anciennes données ? (oui/non): ")
    if response.lower() in ['oui', 'o', 'yes', 'y']:
        migrate_illustrators_from_old_data()
        print("\n" + "=" * 40)
        get_illustrator_stats()
