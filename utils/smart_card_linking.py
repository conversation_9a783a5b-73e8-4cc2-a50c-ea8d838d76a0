#!/usr/bin/env python3
"""
Système de liaison intelligent basé sur vos patterns existants
Analyse vos codes de sets pour créer les correspondances automatiquement
"""

import mysql.connector
import re
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def analyze_existing_patterns():
    """Analyse les patterns de vos sets existants"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("🔍 ANALYSE DES PATTERNS DE SETS EXISTANTS:")
        print("=" * 60)
        
        # Récupérer tous les codes de sets
        cursor.execute("""
            SELECT DISTINCT e.code, e.name_en, e.name_id, e.region,
                   COUNT(pc.id) as card_count
            FROM expansions e
            LEFT JOIN pokemon_cards pc ON e.id = pc.expansion_id
            GROUP BY e.id, e.code, e.name_en, e.name_id, e.region
            ORDER BY e.code
        """)
        
        sets = cursor.fetchall()
        
        # Analyser les patterns
        patterns = {}
        
        for set_data in sets:
            code = set_data['code']
            if not code:
                continue
            
            # Extraire le code base (sans suffixe régional)
            base_code = extract_base_code(code)
            
            if base_code not in patterns:
                patterns[base_code] = []
            
            patterns[base_code].append({
                'code': code,
                'name_en': set_data['name_en'],
                'name_id': set_data['name_id'],
                'region': set_data['region'],
                'card_count': set_data['card_count']
            })
        
        # Afficher les patterns trouvés
        print(f"📊 {len(patterns)} groupes de sets trouvés:")
        
        for base_code, variants in patterns.items():
            if len(variants) > 1:  # Seulement les sets avec plusieurs variantes
                print(f"\n🔗 Groupe: {base_code}")
                for variant in variants:
                    region_flag = get_region_flag(variant['code'])
                    print(f"   {region_flag} {variant['code']}: {variant['card_count']} cartes")
                    if variant['name_en']:
                        print(f"      EN: {variant['name_en']}")
                    if variant['name_id']:
                        print(f"      ID: {variant['name_id']}")
        
        return patterns
        
    except Exception as e:
        print(f"❌ Erreur analyse: {e}")
        return {}
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def extract_base_code(set_code):
    """Extrait le code de base d'un set (sans suffixe régional)"""
    
    # Patterns courants dans vos données
    # SV10S_I → SV10S
    # S7R_TH → S7R
    # SV1S_TH → SV1S
    
    # Supprimer les suffixes régionaux
    base = re.sub(r'_(TH|ID|I|JP|KR|CN|EN)$', '', set_code)
    base = re.sub(r'_I$', '', base)  # Pour les codes comme SV10S_I
    
    return base

def get_region_flag(set_code):
    """Retourne le drapeau emoji pour une région"""
    
    flags = {
        'TH': '🇹🇭',
        'ID': '🇮🇩', 
        'I': '🇮🇩',   # Variante pour indonésien
        'JP': '🇯🇵',
        'KR': '🇰🇷',
        'CN': '🇨🇳',
        'EN': '🇬🇧'
    }
    
    for suffix, flag in flags.items():
        if set_code.endswith(f'_{suffix}') or set_code.endswith(f'_{suffix[0]}'):
            return flag
    
    return '🌐'  # Défaut

def create_automatic_mappings(patterns):
    """Crée automatiquement les correspondances basées sur les patterns"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor()
        
        print("\n🔗 CRÉATION DES CORRESPONDANCES AUTOMATIQUES:")
        print("=" * 60)
        
        created_count = 0
        
        for base_code, variants in patterns.items():
            if len(variants) <= 1:
                continue  # Pas de correspondance possible
            
            # Trouver les codes par région
            thai_code = None
            indonesian_code = None
            common_name = None
            
            for variant in variants:
                code = variant['code']
                if code.endswith('_TH'):
                    thai_code = code
                elif code.endswith('_I') or code.endswith('_ID'):
                    indonesian_code = code
                
                # Utiliser le nom anglais ou indonésien comme nom commun
                if variant['name_en']:
                    common_name = variant['name_en']
                elif variant['name_id'] and not common_name:
                    common_name = variant['name_id']
            
            if not common_name:
                common_name = base_code
            
            # Insérer ou mettre à jour la correspondance
            cursor.execute("""
                INSERT INTO set_mappings (
                    bulbapedia_code, thai_code, common_name, created_at, updated_at
                ) VALUES (%s, %s, %s, NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                    thai_code = VALUES(thai_code),
                    common_name = VALUES(common_name),
                    updated_at = NOW()
            """, (indonesian_code or base_code, thai_code, common_name))
            
            created_count += 1
            print(f"   ✅ {base_code}: {thai_code or 'N/A'} ↔ {indonesian_code or 'N/A'}")
        
        connection.commit()
        print(f"\n🎉 {created_count} correspondances créées/mises à jour")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur création correspondances: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def link_cards_by_patterns():
    """Lie les cartes basées sur les patterns détectés"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("\n🃏 LIAISON DES CARTES PAR PATTERNS:")
        print("=" * 60)
        
        # Récupérer les cartes avec leurs correspondances
        cursor.execute("""
            SELECT 
                pc.id,
                pc.card_number,
                pc.name_en,
                pc.name_th,
                pc.name_id,
                pc.source,
                e.code as expansion_code,
                sm.id as mapping_id,
                sm.common_name
            FROM pokemon_cards pc
            JOIN expansions e ON pc.expansion_id = e.id
            LEFT JOIN set_mappings sm ON (
                sm.thai_code = e.code OR 
                sm.bulbapedia_code = e.code
            )
            WHERE pc.card_number IS NOT NULL
            AND sm.id IS NOT NULL
            ORDER BY sm.id, pc.card_number
        """)
        
        all_cards = cursor.fetchall()
        
        # Grouper par mapping_id et card_number
        card_groups = {}
        
        for card in all_cards:
            mapping_id = card['mapping_id']
            card_number = normalize_card_number(card['card_number'])
            
            key = f"{mapping_id}_{card_number}"
            
            if key not in card_groups:
                card_groups[key] = []
            
            card_groups[key].append(card)
        
        # Créer les liens pour les groupes avec plusieurs cartes
        linked_count = 0
        
        for group_key, cards in card_groups.items():
            if len(cards) > 1:
                # Mettre à jour linked_card_id avec l'ID du premier carte comme référence
                master_id = cards[0]['id']
                
                for card in cards:
                    cursor.execute("""
                        UPDATE pokemon_cards 
                        SET linked_card_id = %s 
                        WHERE id = %s
                    """, (master_id, card['id']))
                
                linked_count += len(cards)
                
                # Afficher le lien créé
                card_names = []
                for card in cards:
                    name = card['name_en'] or card['name_th'] or card['name_id'] or 'Unknown'
                    region = get_region_flag(card['expansion_code'])
                    card_names.append(f"{region} {name}")
                
                print(f"   🔗 {cards[0]['card_number']}: {' ↔ '.join(card_names)}")
        
        connection.commit()
        print(f"\n✅ {linked_count} cartes liées")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur liaison cartes: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def normalize_card_number(card_number):
    """Normalise un numéro de carte"""
    if not card_number:
        return "000"
    
    # Extraire le pattern numéro/total
    match = re.search(r'(\d+)/(\d+)', str(card_number))
    if match:
        return f"{int(match.group(1)):03d}"
    
    # Extraire juste le numéro
    match = re.search(r'(\d+)', str(card_number))
    if match:
        return f"{int(match.group(1)):03d}"
    
    return str(card_number)

def show_linking_results():
    """Affiche les résultats de la liaison"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("\n📊 RÉSULTATS DE LA LIAISON:")
        print("=" * 60)
        
        # Statistiques générales
        cursor.execute("SELECT COUNT(*) as total FROM pokemon_cards")
        total_cards = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as linked FROM pokemon_cards WHERE linked_card_id IS NOT NULL")
        linked_cards = cursor.fetchone()['linked']
        
        print(f"📋 Total cartes: {total_cards:,}")
        print(f"🔗 Cartes liées: {linked_cards:,} ({linked_cards/total_cards*100:.1f}%)")
        
        # Top 5 groupes de cartes liées
        cursor.execute("""
            SELECT 
                linked_card_id,
                COUNT(*) as group_size,
                GROUP_CONCAT(DISTINCT e.code) as set_codes,
                MAX(pc.name_en) as sample_name
            FROM pokemon_cards pc
            JOIN expansions e ON pc.expansion_id = e.id
            WHERE pc.linked_card_id IS NOT NULL
            GROUP BY pc.linked_card_id
            ORDER BY group_size DESC
            LIMIT 5
        """)
        
        top_groups = cursor.fetchall()
        if top_groups:
            print(f"\n🏆 TOP 5 GROUPES DE CARTES LIÉES:")
            for i, group in enumerate(top_groups, 1):
                print(f"   {i}. {group['sample_name'] or 'Unknown'}: {group['group_size']} versions ({group['set_codes']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur statistiques: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    print("🧠 SYSTÈME DE LIAISON INTELLIGENT")
    print("=" * 50)
    
    # 1. Analyser les patterns existants
    patterns = analyze_existing_patterns()
    if not patterns:
        print("❌ Aucun pattern trouvé")
        sys.exit(1)
    
    # 2. Créer les correspondances automatiques
    if not create_automatic_mappings(patterns):
        print("❌ Échec création correspondances")
        sys.exit(1)
    
    # 3. Lier les cartes
    if not link_cards_by_patterns():
        print("❌ Échec liaison cartes")
        sys.exit(1)
    
    # 4. Afficher les résultats
    show_linking_results()
    
    print("\n🎉 LIAISON INTELLIGENTE TERMINÉE!")
    print("✅ Patterns analysés")
    print("✅ Correspondances créées")
    print("✅ Cartes liées")
