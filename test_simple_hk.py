#!/usr/bin/env python3
"""
Test ultra-simple du scraping Traditional Chinese
"""

import requests
from bs4 import BeautifulSoup
import re
import mysql.connector

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
}

DB_CONFIG = {
    'host': 'localhost',
    'user': '<PERSON><PERSON><PERSON>',
    'password': 'Poupouille44',
    'database': 'pokemon_tcg_db',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}

def test_simple():
    print("🇭🇰 TEST ULTRA-SIMPLE")
    print("=" * 30)
    
    # Test 1: Récupérer la première page de cartes
    print("🔍 Test page 1...")
    url = "https://asia.pokemon-card.com/hk/card-search/list/?pageNo=1"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Chercher les liens vers les détails
            detail_links = soup.find_all('a', href=re.compile(r'/detail/\d+'))
            print(f"Liens trouvés: {len(detail_links)}")
            
            if detail_links:
                # Tester la première carte
                first_link = detail_links[0]
                href = first_link.get('href', '')
                card_id_match = re.search(r'/detail/(\d+)', href)
                
                if card_id_match:
                    card_id = card_id_match.group(1)
                    print(f"Première carte ID: {card_id}")
                    
                    # Test 2: Récupérer les détails de cette carte
                    print(f"🔍 Test détails carte {card_id}...")
                    detail_url = f"https://asia.pokemon-card.com/hk/card-search/detail/{card_id}/"
                    
                    detail_response = requests.get(detail_url, headers=HEADERS, timeout=15)
                    print(f"Status détail: {detail_response.status_code}")
                    
                    if detail_response.status_code == 200:
                        detail_soup = BeautifulSoup(detail_response.content, 'html.parser')
                        
                        # Extraire le nom
                        h1 = detail_soup.find('h1')
                        if h1:
                            name = h1.get_text(strip=True)
                            print(f"Nom: {name}")
                        
                        # Extraire le numéro
                        collector_num = detail_soup.find(attrs={'class': 'collectorNumber'})
                        if collector_num:
                            card_number = collector_num.get_text(strip=True)
                            print(f"Numéro: {card_number}")
                        
                        # Test 3: Tester l'URL de l'image
                        image_url = f"https://asia.pokemon-card.com/hk/card-img/hk{int(card_id):08d}.png"
                        print(f"URL image: {image_url}")
                        
                        img_response = requests.head(image_url, headers=HEADERS, timeout=10)
                        print(f"Status image: {img_response.status_code}")
                        
                        # Test 4: Connexion base de données
                        print("🔍 Test connexion DB...")
                        try:
                            connection = mysql.connector.connect(**DB_CONFIG)
                            print("✅ Connexion DB OK")
                            
                            # Vérifier la langue Traditional Chinese
                            cursor = connection.cursor()
                            cursor.execute("SELECT id FROM languages WHERE code = 'zh-tw'")
                            result = cursor.fetchone()
                            if result:
                                print(f"✅ Langue zh-tw trouvée: ID {result[0]}")
                            else:
                                print("❌ Langue zh-tw non trouvée")
                            
                            cursor.close()
                            connection.close()
                            
                        except Exception as e:
                            print(f"❌ Erreur DB: {e}")
                        
                        print("\n✅ TOUS LES TESTS PASSÉS - Le scraper devrait fonctionner !")
                        return True
                    else:
                        print(f"❌ Erreur détail: {detail_response.status_code}")
                else:
                    print("❌ Pas d'ID de carte trouvé")
            else:
                print("❌ Aucun lien de carte trouvé")
        else:
            print(f"❌ Erreur page: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    return False

if __name__ == "__main__":
    test_simple()
