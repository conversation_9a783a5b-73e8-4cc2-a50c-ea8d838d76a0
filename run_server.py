#!/usr/bin/env python3
"""
Simple server launcher for network access
"""

import os
import sys

# Set up environment
project_root = "/media/devmon/piHDD/Sites/pokemon-tcg-scraper"
sys.path.insert(0, project_root)
os.chdir(os.path.join(project_root, "scraping"))

# Remove virtual environment
if 'VIRTUAL_ENV' in os.environ:
    del os.environ['VIRTUAL_ENV']

print("🌐 Pokemon TCG Scraper - Network Server")
print("=" * 50)
print("📍 Local URL: http://127.0.0.1:5051/")
print("🌐 Network URL: http://*************:5051/")
print("🔧 Debug mode: ON")
print("=" * 50)

try:
    from app import app
    
    # Configure for network access
    app.config['DEBUG'] = True
    
    # Start server on all interfaces
    app.run(
        host='0.0.0.0',  # Listen on all interfaces
        port=5051,
        debug=True,
        use_reloader=False,
        threaded=True
    )
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
