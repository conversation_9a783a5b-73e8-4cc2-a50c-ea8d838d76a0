#!/usr/bin/env python3
"""
Simple script to start the web interface - Mac-like experience
"""

import sys
import os

# Disable virtual environment if active
if 'VIRTUAL_ENV' in os.environ:
    del os.environ['VIRTUAL_ENV']

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Change to scraping directory
scraping_dir = os.path.join(project_root, 'scraping')
os.chdir(scraping_dir)

# Import and run the app
try:
    print("🚀 Starting Pokemon TCG Scraper Web Interface...")
    print("📍 URL: http://127.0.0.1:5051/")
    print("🔧 Debug mode: ON")
    print("=" * 50)

    # Import Flask app
    from app import app

    # Configure for local development like on Mac
    app.config['DEBUG'] = True
    app.config['TEMPLATES_AUTO_RELOAD'] = True

    # Start the server
    print("✅ Server starting...")
    app.run(
        host='127.0.0.1',
        port=5051,
        debug=True,
        use_reloader=False,
        threaded=True
    )

except Exception as e:
    print(f"❌ Error starting application: {e}")
    import traceback
    traceback.print_exc()
