#!/usr/bin/env python3
"""
Script de vérification des résultats du scraping Thai
"""

import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def verify_scraping_results():
    """Vérifie les résultats du scraping Thai en cours"""
    
    print("🔍 VÉRIFICATION DES RÉSULTATS DU SCRAPING THAI")
    print("=" * 60)
    
    try:
        # Configuration spécifique pour MariaDB
        db_config = get_db_config()
        # Supprimer le unix_socket pour MariaDB
        if 'unix_socket' in db_config:
            del db_config['unix_socket']

        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        print("✅ Connexion à la base de données établie")
        print(f"🔗 Connecté à: {db_config['host']}:{db_config['port']}/{db_config['database']}")
    except Exception as e:
        print(f"❌ Erreur connexion base: {e}")
        print(f"🔧 Configuration utilisée: {get_db_config()}")
        return
    
    # Récupérer l'ID de la langue thaï
    cursor.execute("SELECT id FROM languages WHERE code = 'th'")
    thai_lang = cursor.fetchone()
    if not thai_lang:
        print("❌ Langue thaï non trouvée")
        return
    
    thai_language_id = thai_lang['id']
    print(f"🇹🇭 ID langue thaï: {thai_language_id}")
    
    print("\n📊 STATISTIQUES GÉNÉRALES:")
    print("-" * 40)
    
    # Compter les expansions
    cursor.execute("SELECT COUNT(*) as count FROM expansions")
    expansions_count = cursor.fetchone()['count']
    print(f"   📦 Expansions totales: {expansions_count}")
    
    # Compter les cartes maîtres
    cursor.execute("SELECT COUNT(*) as count FROM pokemon_cards")
    cards_count = cursor.fetchone()['count']
    print(f"   🃏 Cartes maîtres: {cards_count}")
    
    # Compter les versions thaï
    cursor.execute("SELECT COUNT(*) as count FROM card_versions WHERE language_id = %s", (thai_language_id,))
    thai_versions_count = cursor.fetchone()['count']
    print(f"   🇹🇭 Versions thaï: {thai_versions_count}")
    
    print("\n📦 DÉTAIL PAR EXPANSION:")
    print("-" * 40)
    
    # Détail par expansion avec cartes thaï
    cursor.execute("""
        SELECT
            e.code,
            e.name_en,
            COUNT(DISTINCT pc.id) as total_cards,
            COUNT(DISTINCT cv.id) as thai_cards,
            ROUND((COUNT(DISTINCT cv.id) / COUNT(DISTINCT pc.id)) * 100, 1) as completion_percent
        FROM expansions e
        LEFT JOIN pokemon_cards pc ON e.id = pc.expansion_id
        LEFT JOIN card_versions cv ON pc.id = cv.card_id AND cv.language_id = %s
        GROUP BY e.id, e.code, e.name_en
        HAVING total_cards > 0
        ORDER BY e.id DESC
    """, (thai_language_id,))
    
    expansions = cursor.fetchall()
    
    total_thai_cards = 0
    total_master_cards = 0
    
    for exp in expansions:
        completion = exp['completion_percent'] or 0
        status_icon = "✅" if completion == 100 else "🔄" if completion > 0 else "⭕"
        
        print(f"   {status_icon} {exp['code']:<8} | {exp['thai_cards']:>3}/{exp['total_cards']:<3} cartes | {completion:>5.1f}%")
        
        total_thai_cards += exp['thai_cards'] or 0
        total_master_cards += exp['total_cards'] or 0
    
    print("-" * 40)
    overall_completion = (total_thai_cards / total_master_cards * 100) if total_master_cards > 0 else 0
    print(f"   🎯 TOTAL: {total_thai_cards}/{total_master_cards} cartes ({overall_completion:.1f}%)")
    
    print("\n🕒 DERNIÈRES CARTES AJOUTÉES:")
    print("-" * 40)
    
    # Dernières cartes ajoutées
    cursor.execute("""
        SELECT
            cv.name,
            e.code,
            cv.created_at
        FROM card_versions cv
        JOIN pokemon_cards pc ON cv.card_id = pc.id
        JOIN expansions e ON pc.expansion_id = e.id
        WHERE cv.language_id = %s
        ORDER BY cv.created_at DESC
        LIMIT 10
    """, (thai_language_id,))
    
    recent_cards = cursor.fetchall()
    
    for card in recent_cards:
        print(f"   🆕 {card['name']:<30} | {card['code']:<8} | {card['created_at']}")
    
    print("\n🖼️ IMAGES TÉLÉCHARGÉES:")
    print("-" * 40)
    
    # Compter les images par expansion
    cursor.execute("""
        SELECT
            e.code,
            COUNT(cv.id) as cards_with_images
        FROM expansions e
        JOIN pokemon_cards pc ON e.id = pc.expansion_id
        JOIN card_versions cv ON pc.id = cv.card_id
        WHERE cv.language_id = %s AND cv.image_url IS NOT NULL AND cv.image_url != ''
        GROUP BY e.id, e.code
        ORDER BY e.id DESC
    """, (thai_language_id,))
    
    images_by_expansion = cursor.fetchall()
    
    total_images = 0
    for img_data in images_by_expansion:
        print(f"   🖼️  {img_data['code']:<8} | {img_data['cards_with_images']:>3} images")
        total_images += img_data['cards_with_images']
    
    print(f"   🎯 TOTAL IMAGES: {total_images}")
    
    # Vérifier les sets en cours de scraping
    print("\n🔄 SETS EN COURS DE SCRAPING:")
    print("-" * 40)
    
    cursor.execute("""
        SELECT
            e.code,
            e.name_en,
            COUNT(pc.id) as total_cards,
            COUNT(cv.id) as scraped_cards
        FROM expansions e
        LEFT JOIN pokemon_cards pc ON e.id = pc.expansion_id
        LEFT JOIN card_versions cv ON pc.id = cv.card_id AND cv.language_id = %s
        GROUP BY e.id
        HAVING total_cards > 0 AND scraped_cards < total_cards
        ORDER BY scraped_cards DESC
    """, (thai_language_id,))
    
    in_progress = cursor.fetchall()
    
    if in_progress:
        for prog in in_progress:
            completion = (prog['scraped_cards'] / prog['total_cards'] * 100) if prog['total_cards'] > 0 else 0
            print(f"   🔄 {prog['code']:<8} | {prog['scraped_cards']:>3}/{prog['total_cards']:<3} | {completion:>5.1f}%")
    else:
        print("   ✅ Aucun set en cours de scraping détecté")
    
    connection.close()
    
    print(f"\n✅ VÉRIFICATION TERMINÉE")
    print(f"🎯 Résumé: {total_thai_cards} cartes thaï dans {len(expansions)} expansions")

if __name__ == "__main__":
    verify_scraping_results()
