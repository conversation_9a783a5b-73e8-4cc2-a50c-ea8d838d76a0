# 🤖 CONTEXTE POUR LA PROCHAINE IA AUGMENT

## 📋 RÉSUMÉ DU PROJET

**Projet** : Pokemon TCG Scraper Multilingue  
**Objectif** : Scraper automatiquement TOUTES les cartes Pokémon depuis les sites officiels en plusieurs langues  
**Utilisateur** : <PERSON><PERSON><PERSON> (préfère qu'on l'appelle par son nom)

## 🌍 LANGUES SUPPORTÉES (ÉTAT ACTUEL)

### ✅ SCRAPERS FONCTIONNELS :
1. **🇹🇭 Thaï** - `scraping_thai.py` - ✅ PARFAIT
   - Source : asia.pokemon-card.com/th/
   - Détecte automatiquement tous les sets
   - Images dans dossiers par set (ex: S10A_TH/)

2. **🇮🇩 Indonésien** - `scraping_indonesian.py` - ✅ PARFAIT  
   - Source : asia.pokemon-card.com/id/
   - Détecte automatiquement tous les sets
   - Images dans dossiers par set (ex: S10A_I/)

3. **🇭🇰 Traditional Chinese** - `scraping_traditional_chinese.py` - ✅ PARFAIT
   - Source : asia.pokemon-card.com/hk/
   - Détecte automatiquement tous les sets
   - Images dans dossiers par set (ex: HK13_ZH_TW/)

### ❌ SCRAPER EN COURS DE CORRECTION :
4. **🇯🇵 Japonais** - `scraping_japanese.py` - ⚠️ PROBLÈME À RÉSOUDRE

## 🚨 PROBLÈME ACTUEL - SCRAPER JAPONAIS

### CONTEXTE :
- Le scraper japonais a été créé mais ne fonctionne pas comme les autres
- L'utilisateur veut la MÊME LOGIQUE que les scrapers thaï/indonésien/chinois
- Source : www.pokemon-card.com/

### PROBLÈME IDENTIFIÉ :
Le scraper japonais essaie de deviner les IDs de cartes (1, 2, 3, 4...) au lieu de :
1. Détecter automatiquement tous les sets depuis le site
2. Parcourir chaque set individuellement
3. Récupérer toutes les cartes de chaque set

### URLS IMPORTANTES FOURNIES PAR L'UTILISATEUR :
- **Page produits/expansions** : https://www.pokemon-card.com/products/index.html?productType=expansion
- **Page de recherche** : https://www.pokemon-card.com/card-search/index.php?keyword=&se_ta=&regulation_sidebar_form=all&pg=&illust=&sm_and_keyword=true
- **Exemple carte** : https://www.pokemon-card.com/card-search/details.php/card/47537/
- **Exemple image** : https://www.pokemon-card.com/assets/images/card_images/large/SV11B/047537_P_TSUTAJIXYA.jpg

## 🎯 SOLUTION REQUISE

### CE QUE L'UTILISATEUR VEUT :
1. **Même logique** que les scrapers thaï/indonésien/chinois
2. **Détection automatique** de tous les sets depuis le site japonais
3. **Parcours de chaque set** individuellement
4. **Récupération de TOUTES les cartes** existantes et futures
5. **PAS d'arrêt sur échecs consécutifs** (l'utilisateur a insisté là-dessus)

### STRUCTURE ATTENDUE :
```
/images/
├── SV10_JA/          # Set japonais SV10
├── SV11B_JA/         # Set japonais SV11B  
├── ENE_JA/           # Set énergies japonais
└── ...
```

### FORMAT DES FICHIERS :
- **Nom fichier** : `{ID}_JA.jpg` (ex: 47537_JA.jpg)
- **Dossier** : `{SET_CODE}_JA/` (ex: SV11B_JA/)
- **Chemin complet** : `images/SV11B_JA/47537_JA.jpg`

## 🔧 ARCHITECTURE TECHNIQUE

### BASE DE DONNÉES :
- **pokemon_cards** : Cartes maîtres
- **card_versions** : Versions par langue (avec `local_image_path`)
- **expansions** : Sets/Expansions  
- **languages** : Langues (japonais = ID 4, code 'ja')

### CONFIGURATION DB :
```python
DB_CONFIG = {
    'host': 'localhost',
    'user': 'Munjiko', 
    'password': 'Poupouille44',
    'database': 'pokemon_tcg_db'
}
```

### RÈGLES NON NÉGOCIABLES (MÉMOIRES UTILISATEUR) :
- **Jamais de hardcoding** des sets ou IDs
- **Détection automatique** des nouveaux sets (sortent chaque mois)
- **Gestion des petits sets** avec peu de cartes dispersées
- **Images locales** avec chemins relatifs (pas absolus)
- **Structure cohérente** entre toutes les langues

## 📁 STRUCTURE DU PROJET

```
pokemon-tcg-scraper/
├── scrap-pkmn.py              # Menu principal
├── scraping/
│   ├── app.py                 # Interface web Flask
│   ├── scraping_thai.py       # ✅ Scraper thaï (MODÈLE)
│   ├── scraping_indonesian.py # ✅ Scraper indonésien (MODÈLE)  
│   ├── scraping_traditional_chinese.py # ✅ Scraper chinois (MODÈLE)
│   └── scraping_japanese.py   # ❌ À corriger
├── images/                    # Images par set et langue
└── venv/                      # Environnement virtuel
```

## 🎯 TÂCHE POUR LA PROCHAINE IA

### OBJECTIF PRINCIPAL :
Corriger `scraping_japanese.py` pour qu'il fonctionne EXACTEMENT comme les autres scrapers.

### ÉTAPES RECOMMANDÉES :
1. **Analyser** les scrapers thaï/indonésien/chinois qui fonctionnent
2. **Identifier** leur logique de détection des sets
3. **Adapter** cette logique au site japonais
4. **Utiliser** les URLs fournies par l'utilisateur
5. **Tester** avec quelques cartes connues (47537, etc.)

### CARTES DE TEST CONNUES :
- **ID 47537** : ツタージャ (Tsutarja/Snivy) - Set SV11B
- **IDs 1-8** : Énergies - Set ENE
- **IDs 47530+** : Cartes récentes - Sets SV10, SV11B

### POINTS D'ATTENTION :
- L'utilisateur veut **PAS d'arrêt sur échecs consécutifs**
- Le site japonais peut utiliser du **JavaScript** (plus complexe)
- Les **URLs directes** `/card/{ID}/` fonctionnent
- La **détection des sets** doit être automatique

## 💬 STYLE DE COMMUNICATION

L'utilisateur Munjiko :
- Préfère les **solutions directes** et efficaces
- N'aime pas les **complications inutiles**
- Veut la **même logique** que ce qui fonctionne déjà
- Apprécie les **explications techniques** claires
- Utilise souvent "pk" au lieu de "pourquoi"

## 🎉 OBJECTIF FINAL

Un scraper japonais qui :
1. ✅ Détecte automatiquement tous les sets japonais
2. ✅ Récupère toutes les cartes de chaque set  
3. ✅ Sauvegarde dans la même structure que les autres langues
4. ✅ Fonctionne pour les cartes actuelles ET futures
5. ✅ Ne s'arrête jamais sur des échecs consécutifs

**Bonne chance ! 🚀**
