-- Pokemon TCG Database Schema - Unified System
-- This file contains the complete database structure for the Pokemon TCG scraper
-- Compatible with MariaDB/MySQL

-- Database creation
CREATE DATABASE IF NOT EXISTS pokemon_tcg_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE pokemon_tcg_db;

-- Table for Languages
CREATE TABLE IF NOT EXISTS `languages` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(5) UNIQUE NOT NULL,
    `name` VARCHAR(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Expansions (Sets)
CREATE TABLE IF NOT EXISTS `expansions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(20) UNIQUE NOT NULL,
    `name_en` VARCHAR(255),
    `name_id` VARCHA<PERSON>(255),
    `release_date` DATE,
    `region` VARCHAR(50)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Pokemon Illustrators
CREATE TABLE IF NOT EXISTS `pokemon_illustrators` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Pokemon Cards (Master Cards)
CREATE TABLE IF NOT EXISTS `pokemon_cards` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `expansion_id` INT,
    `card_number` VARCHAR(50),
    `illustrator_id` INT,
    `source` VARCHAR(255),
    `linked_card_id` INT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`expansion_id`) REFERENCES `expansions`(`id`),
    FOREIGN KEY (`illustrator_id`) REFERENCES `pokemon_illustrators`(`id`),
    INDEX `idx_expansion` (`expansion_id`),
    INDEX `idx_card_number` (`card_number`),
    INDEX `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Card Versions (Language-specific versions)
CREATE TABLE IF NOT EXISTS `card_versions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `card_id` INT NOT NULL,
    `language_id` INT NOT NULL,
    `name` VARCHAR(255),
    `rarity` VARCHAR(50),
    `image_url` TEXT,
    `web_id` VARCHAR(100),
    `source` VARCHAR(50),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`card_id`) REFERENCES `pokemon_cards`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`language_id`) REFERENCES `languages`(`id`),
    INDEX `idx_card_language` (`card_id`, `language_id`),
    INDEX `idx_web_id` (`web_id`),
    INDEX `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for Set Mappings (Cross-language set relationships)
CREATE TABLE IF NOT EXISTS `set_mappings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `bulbapedia_code` VARCHAR(50),
    `thai_code` VARCHAR(50),
    `common_name` VARCHAR(255),
    `release_date` DATE,
    `is_variant` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_bulbapedia_code` (`bulbapedia_code`),
    INDEX `idx_thai_code` (`thai_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default languages
INSERT IGNORE INTO `languages` (`code`, `name`) VALUES
('en', 'English'),
('th', 'ไทย (Thai)'),
('id', 'Bahasa Indonesia'),
('ja', '日本語 (Japanese)'),
('ko', '한국어 (Korean)'),
('zh', '中文 (Chinese)'),
('fr', 'Français'),
('de', 'Deutsch'),
('es', 'Español'),
('it', 'Italiano');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS `idx_card_versions_lookup` ON `card_versions` (`language_id`, `web_id`);
CREATE INDEX IF NOT EXISTS `idx_pokemon_cards_lookup` ON `pokemon_cards` (`expansion_id`, `card_number`);
CREATE INDEX IF NOT EXISTS `idx_expansions_code` ON `expansions` (`code`);

-- Views for compatibility (optional)
CREATE OR REPLACE VIEW `cards_view` AS
SELECT 
    pc.id,
    pc.card_number as canonical_number,
    pc.expansion_id,
    pc.illustrator_id,
    pc.source,
    pc.created_at,
    pc.updated_at
FROM pokemon_cards pc;

-- Statistics view
CREATE OR REPLACE VIEW `scraping_stats` AS
SELECT 
    e.code as expansion_code,
    e.name_en as expansion_name,
    l.code as language_code,
    l.name as language_name,
    COUNT(DISTINCT pc.id) as total_cards,
    COUNT(DISTINCT cv.id) as scraped_cards,
    ROUND((COUNT(DISTINCT cv.id) / COUNT(DISTINCT pc.id)) * 100, 2) as completion_percentage
FROM expansions e
LEFT JOIN pokemon_cards pc ON e.id = pc.expansion_id
LEFT JOIN card_versions cv ON pc.id = cv.card_id
LEFT JOIN languages l ON cv.language_id = l.id
GROUP BY e.id, e.code, e.name_en, l.id, l.code, l.name
HAVING total_cards > 0
ORDER BY e.code, l.code;
