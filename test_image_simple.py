#!/usr/bin/env python3
"""Test simple du téléchargement d'images indonésiennes"""

import sys
import os
sys.path.append('.')

from scraping.scraping_indonesian import download_image, generate_image_filename

def test_simple():
    """Test simple avec données fixes"""
    
    print("🧪 TEST SIMPLE DU TÉLÉCHARGEMENT D'IMAGES")
    print("=" * 50)
    
    # Données de test avec vraie URL d'image
    test_card = {
        'webId': '15861',
        'nameCard': 'Pinsir <Ethan>',
        'numberCard': '001/138',
        'imageUrl': 'https://asia.pokemon-card.com/id/card-img/id00015861.png'
    }
    
    set_code = 'SV10S'
    
    # Test 1: Génération du nom de fichier
    print("\n1️⃣ Test génération nom de fichier:")
    try:
        filename = generate_image_filename(test_card, set_code)
        print(f"   ✅ Nom généré: {filename}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return
    
    # Test 2: Création du dossier
    print("\n2️⃣ Création du dossier:")
    folder_path = f"images/{set_code}_I"
    try:
        os.makedirs(folder_path, exist_ok=True)
        print(f"   ✅ Dossier créé: {folder_path}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return
    
    # Test 3: Téléchargement d'image
    print("\n3️⃣ Test téléchargement image:")
    local_path = f"{folder_path}/{filename}"
    print(f"   📁 Chemin local: {local_path}")
    print(f"   🌐 URL source: {test_card['imageUrl']}")
    
    try:
        success = download_image(test_card['imageUrl'], local_path)
        if success:
            print(f"   ✅ Image téléchargée avec succès!")
            if os.path.exists(local_path):
                size = os.path.getsize(local_path)
                print(f"   📏 Taille: {size} bytes")
            else:
                print(f"   ❌ Fichier non trouvé après téléchargement")
        else:
            print(f"   ❌ Échec du téléchargement")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    print("\n🏁 Test terminé!")

if __name__ == "__main__":
    test_simple()
