{% extends "base.html" %}

{% block title %}Pokemon Sets - Pokemon TCG Database{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">📚 Pokemon TCG Sets</h1>
    <p class="page-subtitle">Browse and explore {{ "{:,}".format(sets|length) }} Pokemon TCG sets with multilingual support</p>
</div>

<div class="content-card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; flex-wrap: wrap; gap: 1rem;">
        <h2 style="color: #1f2937; margin: 0;">Set Collection</h2>

        <!-- Language filter removed - sets exist in multiple languages simultaneously -->
    </div>

    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>Set Code</th>
                    <th>Name</th>
                    <th>Cards</th>
                    <th>Status</th>
                    <!-- Always show main languages for scraping preparation -->
                    <th>EN</th>
                    <th>TH</th>
                    <th>ID</th>
                    <th>JA</th>
                    <th>KO</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for s in sets %}
                <tr>
                    <td>
                        <strong style="color: #4f46e5;">{{ s.code }}</strong>
                        {% if s.has_variants %}
                        <div style="font-size: 0.75rem; color: #059669; margin-top: 0.25rem;">
                            🔗 Variants:
                            {% for related in s.related_sets %}
                                {% if related != s.code %}
                                    <span style="background: #ecfdf5; padding: 0.125rem 0.25rem; border-radius: 0.25rem; margin-right: 0.25rem;">{{ related }}</span>
                                {% endif %}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </td>
                    <td>
                        <div>{{ s.name_en or s.english_name or s.name }}</div>
                        {% if s.name_en and s.name != s.name_en %}
                        <div style="font-size: 0.8rem; color: #6b7280;">{{ s.name }}</div>
                        {% endif %}
                    </td>
                    <td>
                        <strong style="color: #059669;">{{ s.card_count or 0 }}</strong>
                    </td>
                    <td>
                        {% if s.is_global_name %}
                            <span class="badge badge-success">✓ Global</span>
                        {% else %}
                            <span class="badge">Local</span>
                        {% endif %}
                    </td>
                    {% if display_langs|length > 1 %}
                        {% for lang in display_langs %}
                            <td style="font-size: 0.875rem;">{{ s.multilang_names[lang] or '-' }}</td>
                        {% endfor %}
                    {% endif %}
                    <td>
                        <a href="{{ url_for('show_cards', set_code=s.code) }}" class="btn" style="padding: 0.5rem 1rem; font-size: 0.875rem;">
                            View Cards
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {% if sets|length == 0 %}
    <div style="text-align: center; padding: 3rem; color: #6b7280;">
        <div style="font-size: 4rem; margin-bottom: 1rem;">📭</div>
        <h3>No sets found</h3>
        <p>Check back later for more sets.</p>
    </div>
    {% endif %}
</div>

<div class="content-card">
    <h3 style="color: #1f2937; margin-bottom: 1rem;">📊 Quick Stats</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
            <div style="font-size: 1.5rem; font-weight: bold; color: #4f46e5;">{{ "{:,}".format(sets|length) }}</div>
            <div style="font-size: 0.875rem; color: #6b7280;">Total Sets</div>
        </div>
        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
            <div style="font-size: 1.5rem; font-weight: bold; color: #4f46e5;">{{ sets|selectattr('total_cards')|map(attribute='total_cards')|sum }}</div>
            <div style="font-size: 0.875rem; color: #6b7280;">Total Cards</div>
        </div>
        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
            <div style="font-size: 1.5rem; font-weight: bold; color: #4f46e5;">
                {{ sets|selectattr('is_global_name')|list|length }}
            </div>
            <div style="font-size: 0.875rem; color: #6b7280;">Global Sets</div>
        </div>
        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
            <div style="font-size: 1.5rem; font-weight: bold; color: #4f46e5;">
                {{ sets|selectattr('english_name')|list|length }}
            </div>
            <div style="font-size: 0.875rem; color: #6b7280;">With English Names</div>
        </div>
    </div>
</div>
{% endblock %}