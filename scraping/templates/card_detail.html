<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title><PERSON>é<PERSON> de la carte {{ card.name_th or card.name_en or card.name_id }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .card-versions { margin-top: 30px; padding: 20px; border: 2px solid #ddd; border-radius: 10px; }
        .version-item { display: inline-block; margin: 10px; padding: 15px; border: 1px solid #ccc; border-radius: 8px; text-align: center; }
        .version-item img { max-width: 150px; height: auto; }
        .flag { font-size: 1.5em; }
    </style>
</head>
<body>
    <h1>🃏 Détail de la carte</h1>
    <a href="{{ url_for('show_cards', set_code=card.expansion_code) }}">&larr; Retour au set {{ card.expansion_code }}</a>

    <table border="1" cellpadding="10" style="margin-top: 20px;">
        <tr><th>N<PERSON><PERSON>ro</th><td>{{ card.card_number }}</td></tr>
        <tr><th>Nom (EN)</th><td>{{ card.name_en or 'N/A' }}</td></tr>
        <tr><th>Nom (TH)</th><td>{{ card.name_th or 'N/A' }}</td></tr>
        <tr><th>Nom (ID)</th><td>{{ card.name_id or 'N/A' }}</td></tr>
        <tr><th>Set</th><td>{{ card.expansion_name or card.expansion_code }}</td></tr>
        <tr><th>Illustrateur</th><td>{{ card.illustrator_name or 'N/A' }}</td></tr>
        <tr><th>Type</th><td>{{ card.card_type or 'N/A' }}</td></tr>
        <tr><th>HP</th><td>{{ card.hp or 'N/A' }}</td></tr>
    </table>

    <!-- Images disponibles -->
    <div style="margin-top: 20px;">
        <h3>🖼️ Images disponibles:</h3>
        {% if card.image_url_th %}
            <div style="display: inline-block; margin: 10px;">
                <div>🇹🇭 Thaï</div>
                <img src="{{ card.image_url_th }}" alt="Image thaï" width="200">
            </div>
        {% endif %}
        {% if card.image_url_id %}
            <div style="display: inline-block; margin: 10px;">
                <div>🇮🇩 Indonésien</div>
                <img src="{{ card.image_url_id }}" alt="Image indonésienne" width="200">
            </div>
        {% endif %}
        {% if card.image_url %}
            <div style="display: inline-block; margin: 10px;">
                <div>🌐 Autre</div>
                <img src="{{ card.image_url }}" alt="Image" width="200">
            </div>
        {% endif %}
    </div>

    <!-- Versions liées -->
    {% if linked_cards %}
    <div class="card-versions">
        <h3>🔗 Autres versions de cette carte:</h3>
        {% for linked in linked_cards %}
        <div class="version-item">
            <div class="flag">
                {% if linked.expansion_code.endswith('_TH') %}🇹🇭
                {% elif linked.expansion_code.endswith('_I') or linked.expansion_code.endswith('_ID') %}🇮🇩
                {% elif linked.expansion_code.endswith('_JP') %}🇯🇵
                {% else %}🌐{% endif %}
            </div>
            <div><strong>{{ linked.name_en or linked.name_th or linked.name_id or 'Unknown' }}</strong></div>
            <div>{{ linked.expansion_code }}</div>
            {% if linked.image_url_th or linked.image_url_id or linked.image_url %}
                <img src="{{ linked.image_url_th or linked.image_url_id or linked.image_url }}" alt="Version liée">
            {% endif %}
            <div><a href="{{ url_for('card_detail', card_id=linked.id) }}">Voir cette version</a></div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

</body>
</html>