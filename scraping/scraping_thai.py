#!/usr/bin/env python3
"""
Scraper <PERSON><PERSON><PERSON> - <PERSON><PERSON>mon TCG
Récupère TOUS les sets et TOUTES les cartes depuis le site officiel thaï

RÈGLES NON NÉGOCIABLES :
- Scraping complet de tous les sets disponibles
- Récupération de toutes les cartes de chaque set (pas seulement 20)
- Fonctionnement autonome et indépendant
- Source unique : asia.pokemon-card.com/th/
"""

import requests
from bs4 import BeautifulSoup
import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config
from utils.illustrator_utils import get_or_create_illustrator
import time
import random
import logging
import argparse
import re
from urllib.parse import urljoin

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# URLs du site thaï
BASE_URL = "https://asia.pokemon-card.com"
LIST_URL = "https://asia.pokemon-card.com/th/card-search/list/?expansionCodes="

def download_image(image_url, local_path, max_retries=3):
    """Télécharge une image depuis une URL"""
    
    if os.path.exists(local_path):
        logging.info(f"L'image existe déjà: {local_path}")
        return True
    
    # Créer le dossier si nécessaire
    os.makedirs(os.path.dirname(local_path), exist_ok=True)
    
    for attempt in range(max_retries):
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Language': 'th-TH,th;q=0.9,en-US;q=0.8,en;q=0.7',
                'Referer': 'https://asia.pokemon-card.com/'
            }
            
            response = requests.get(image_url, timeout=15, headers=headers)
            if response.status_code == 200:
                with open(local_path, 'wb') as f:
                    f.write(response.content)
                logging.info(f"Image téléchargée: {local_path}")
                return True
        except Exception as e:
            logging.warning(f"Tentative {attempt + 1}/{max_retries} échouée pour {image_url}: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
    
    logging.error(f"Échec du téléchargement: {image_url}")
    return False

def generate_image_filename(card_details, set_code):
    """Génère un nom de fichier pour l'image de la carte (format identique à l'indonésien)"""
    card_number = card_details.get('card_number', '000')

    # Vérifier que card_number n'est pas None
    if card_number is None:
        card_number = '000'

    # Nettoyer le numéro de carte (prendre seulement la partie avant le /)
    if '/' in card_number:
        clean_number = card_number.split('/')[0].zfill(3)
        total_cards = card_number.split('/')[1].zfill(3)
    else:
        # Si pas de format X/Y, utiliser le numéro tel quel
        clean_number = str(card_number).zfill(3)
        total_cards = "999"  # Valeur par défaut

    return f"{clean_number}_{total_cards}.jpg"

def extract_real_set_info(soup):
    """Extrait les VRAIES informations du set depuis la page"""

    set_info = {
        'code': None,
        'name_th': None,
        'name_en': None
    }

    try:
        # Chercher le lien vers le set (méthode principale)
        set_links = soup.find_all('a', href=re.compile(r'expansionCodes='))

        for link in set_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)

            # Extraire le code du set depuis l'URL
            code_match = re.search(r'expansionCodes=([^&]+)', href)
            if code_match:
                set_info['code'] = code_match.group(1)
                set_info['name_th'] = text

                # Essayer de deviner le nom anglais
                if 'starter' in text.lower():
                    set_info['name_en'] = f"Starter Deck {set_info['code']}"
                elif 'sv' in set_info['code'].lower():
                    set_info['name_en'] = f"Scarlet & Violet {set_info['code']}"
                else:
                    set_info['name_en'] = f"Set {set_info['code']}"

                break

        # Fallback: chercher dans le texte de la page
        if not set_info['code']:
            page_text = soup.get_text()

            # Patterns de codes de sets
            code_patterns = [
                r'\b(SC\d+[A-Z]*)\b',    # SC1D, SC2, etc.
                r'\b(SV\d+[a-z]*)\b',    # SV1s, SV2a, etc.
                r'\b(S\d+[a-z]*)\b',     # S1, S2a, etc.
            ]

            for pattern in code_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    set_info['code'] = matches[0].upper()
                    set_info['name_en'] = f"Set {set_info['code']}"
                    break

    except Exception as e:
        logger.error(f"Erreur extraction set info: {e}")

    return set_info

def get_card_details(card_id):
    """Récupère les détails d'une carte par son ID avec VRAI set et gestion d'erreur robuste"""

    detail_url = f"https://asia.pokemon-card.com/th/card-search/detail/{card_id}/"

    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            # Initialiser les variables par défaut
            image_url = ''
            illustrator = ''
            
            # Headers pour éviter les blocages
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'th-TH,th;q=0.9,en-US;q=0.8,en;q=0.7',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            response = requests.get(detail_url, timeout=15, headers=headers)
            if response.status_code != 200:
                return None

            soup = BeautifulSoup(response.content, 'html.parser')

            # Extraire le nom thaï
            name_element = soup.find('h1', class_='pageHeader')
            if not name_element:
                return None

            # Nettoyer le nom (supprimer les spans)
            for span in name_element.find_all('span'):
                span.decompose()
            name_th = name_element.get_text(strip=True)

            # FILTRER LES PAGES D'ERREUR
            if "ผลลัพธ์การค้นหาการ์ด" in name_th:
                return None

            # Extraire le numéro de carte
            number_element = soup.find('span', string=re.compile(r'\d+/\d+'))
            card_number = number_element.get_text(strip=True) if number_element else "???"

            if card_number == "???":
                return None

            # Construire l'URL de l'image directement à partir de l'ID
            # Format: https://asia.pokemon-card.com/th/card-img/th{ID}.png
            image_url = f"https://asia.pokemon-card.com/th/card-img/th{card_id:08d}.png"

            # NOUVEAU: Extraire les VRAIES informations du set
            set_info = extract_real_set_info(soup)

            # Extraire l'illustrateur
            illustrator_div = soup.find('div', class_='illustrator')
            if illustrator_div:
                illustrator_link = illustrator_div.find('a')
                if illustrator_link:
                    illustrator = illustrator_link.get_text(strip=True)

            return {
                'card_id': card_id,
                'web_id': card_id,
                'name_th': name_th,
                'card_number': card_number,
                'image_url': image_url,
                'illustrator': illustrator,
                'set_code': set_info['code'],
                'set_name_th': set_info['name_th'],
                'set_name_en': set_info['name_en']
            }

        except requests.exceptions.Timeout:
            print(f"      ⏱️  Timeout pour l'ID {card_id} (tentative {attempt + 1}/{max_retries})")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            return None

        except requests.exceptions.ConnectionError:
            print(f"      🔌 Erreur de connexion pour l'ID {card_id} (tentative {attempt + 1}/{max_retries})")
            if attempt < max_retries - 1:
                time.sleep(retry_delay * 2)  # Plus long pour les erreurs de connexion
                continue
            return None

        except requests.exceptions.RequestException as e:
            print(f"      ❌ Erreur réseau pour l'ID {card_id}: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            return None

        except Exception as e:
            logger.error(f"Erreur récupération détails carte {card_id}: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            return None

    return None

def find_latest_card_id():
    """Trouve automatiquement l'ID de la dernière carte disponible"""
    print("🔍 RECHERCHE AUTOMATIQUE DE LA DERNIÈRE CARTE...")

    # Commencer par tester des IDs élevés pour trouver la zone active
    test_ranges = [
        (20000, 25000),  # Zone probable des cartes récentes
        (15000, 20000),  # Zone intermédiaire
        (10000, 15000),  # Zone plus ancienne
        (25000, 30000),  # Au cas où il y aurait encore plus
        (30000, 50000),  # Zone très récente
    ]

    latest_found = 0

    for start, end in test_ranges:
        print(f"   🔍 Test range {start}-{end}...")

        # Tester quelques IDs dans cette range
        test_ids = [start, start + 500, start + 1000, end - 1000, end - 500, end]

        for test_id in test_ids:
            card_details = get_card_details(test_id)
            if card_details:
                latest_found = max(latest_found, test_id)
                print(f"   ✅ Carte trouvée à l'ID {test_id}: {card_details['name_th']}")
                break

    # Affiner la recherche autour de la zone trouvée
    if latest_found > 0:
        print(f"🎯 Affinement autour de l'ID {latest_found}...")

        # Chercher vers le haut pour trouver la vraie limite
        search_id = latest_found
        while search_id < latest_found + 5000:
            if get_card_details(search_id):
                latest_found = search_id
                search_id += 100
            else:
                search_id += 100
                if search_id - latest_found > 1000:  # Gap trop grand
                    break

    final_max = latest_found + 2000 if latest_found > 0 else 25000
    print(f"✅ Limite dynamique déterminée: {final_max}")
    return final_max

def get_all_expansion_codes():
    """Récupère tous les codes d'expansion depuis la page principale (checkboxes)"""
    print("🔍 RÉCUPÉRATION DE TOUS LES SETS DISPONIBLES...")

    try:
        response = requests.get("https://asia.pokemon-card.com/th/card-search/", timeout=15)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # Chercher tous les checkboxes avec class="expansionCode"
        expansion_checkboxes = soup.find_all('input', {'class': 'expansionCode', 'type': 'checkbox'})
        expansion_codes = []

        print("📦 SETS TROUVÉS:")
        for checkbox in expansion_checkboxes:
            code = checkbox.get('value', '').strip()
            if code and code not in expansion_codes:
                expansion_codes.append(code)

                # Chercher le label associé pour le nom
                set_name = f"Set {code}"
                label_for = checkbox.get('id', '')
                if label_for:
                    label = soup.find('label', {'for': label_for})
                    if label:
                        set_name = label.get_text(strip=True) or f"Set {code}"

                print(f"   ✅ {code}: {set_name}")

        # Trier les codes pour un ordre logique
        expansion_codes.sort()

        print(f"\n📊 TOTAL: {len(expansion_codes)} sets trouvés")
        print(f"🎯 SETS PROMO DÉTECTÉS: {len([c for c in expansion_codes if 'P' in c or 'promo' in c.lower()])}")
        print(f"🎯 SETS PRINCIPAUX: {len([c for c in expansion_codes if c.startswith(('SV', 'S', 'SC'))])}")

        return expansion_codes

    except Exception as e:
        print(f"❌ Erreur récupération sets: {e}")
        return []

def scrape_expansion_cards(connection, expansion_code):
    """Scrape toutes les cartes d'un set spécifique"""
    print(f"\n🎯 SCRAPING SET: {expansion_code}")
    print("-" * 50)

    thai_language_id = get_language_id(connection, 'th')
    if not thai_language_id:
        print("❌ Erreur: Langue thaï non trouvée dans la base")
        return 0

    saved_count = 0
    page = 1

    while True:
        try:
            # URL pour récupérer les cartes du set
            url = f"https://asia.pokemon-card.com/th/card-search/list/?expansionCodes={expansion_code}&pageNo={page}"

            response = requests.get(url, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Chercher les cartes sur cette page (liens vers /detail/)
            card_elements = soup.find_all('a', href=re.compile(r'/card-search/detail/\d+/'))

            if not card_elements:
                print(f"   📄 Page {page}: Aucune carte trouvée - fin du set")
                break

            print(f"   📄 Page {page}: {len(card_elements)} cartes trouvées")

            for card_element in card_elements:
                try:
                    # Extraire les détails de la carte depuis l'élément
                    card_details = extract_card_from_element(card_element, expansion_code)

                    if card_details:
                        # Sauvegarder la carte (la fonction gère déjà les doublons)
                        result = save_card_to_db(connection, card_details, thai_language_id)
                        if result:
                            saved_count += 1
                            print(f"      💾 {card_details['name']} - Sauvegardée")
                        else:
                            print(f"      ⚠️  {card_details['name']} - Déjà existante ou erreur")

                except Exception as e:
                    print(f"      ❌ Erreur traitement carte: {e}")
                    continue

            page += 1
            time.sleep(1)  # Pause entre les pages

        except Exception as e:
            print(f"❌ Erreur page {page}: {e}")
            break

    return saved_count

def scrape_all_thai_cards_progressive(connection):
    """Scrape TOUTES les cartes par sets (nouvelle méthode)"""
    print(f"\n🎯 SCRAPING PROGRESSIF DE TOUTES LES CARTES THAÏ")
    print("=" * 60)

    # Récupérer tous les codes d'expansion
    expansion_codes = get_all_expansion_codes()

    if not expansion_codes:
        print("❌ Aucun set trouvé")
        return 0

    total_saved = 0

    for i, expansion_code in enumerate(expansion_codes, 1):
        print(f"\n📦 SET {i}/{len(expansion_codes)}: {expansion_code}")
        saved_count = scrape_expansion_cards(connection, expansion_code)
        total_saved += saved_count
        print(f"   ✅ {saved_count} nouvelles cartes sauvegardées")

        # Pause entre les sets
        time.sleep(2)

    print(f"\n📊 RÉSULTAT FINAL:")
    print(f"   • Sets traités: {len(expansion_codes)}")
    print(f"   • Cartes sauvegardées: {total_saved}")

    return total_saved

def extract_card_from_element(card_element, expansion_code):
    """Extrait les détails d'une carte depuis un élément HTML (lien direct)"""
    try:
        # Initialiser les variables par défaut
        image_url = ''
        
        # L'élément est déjà le lien vers la carte
        href = card_element.get('href', '')
        if not href:
            return None

        # Extraire l'ID de la carte depuis l'URL
        card_id_match = re.search(r'/detail/(\d+)/', href)
        if not card_id_match:
            return None

        card_id = int(card_id_match.group(1))

        # Récupérer les vrais détails de la carte
        print(f"      🔍 Récupération détails carte {card_id}...")
        card_details = get_card_details(card_id)

        if card_details:
            card_name = card_details.get('name_th', f"Card {card_id}")
            card_number = card_details.get('card_number', f"{card_id:03d}")
            illustrator = card_details.get('illustrator', '')
            # Utiliser l'image de la page de détail si disponible
            if card_details.get('image_url'):
                image_url = card_details['image_url']
        else:
            card_name = f"Card {card_id}"
            card_number = f"{card_id:03d}"
            illustrator = ''
            # S'assurer que image_url est définie même si card_details est None
            image_url = ''

        # Si pas d'image récupérée des détails, construire l'URL directement
        if not image_url:
            # Format: https://asia.pokemon-card.com/th/card-img/th{ID}.png
            image_url = f"https://asia.pokemon-card.com/th/card-img/th{card_id:08d}.png"

        return {
            'web_id': card_id,
            'name': card_name,
            'card_number': card_number,
            'image_url': image_url,
            'illustrator': illustrator,
            'set_code': expansion_code,
            'set_name_en': f"Set {expansion_code}",
            'set_name_th': f"Set {expansion_code}"
        }

    except Exception as e:
        print(f"      ❌ Erreur extraction carte: {e}")
        return None

def save_card_to_db(connection, card_details, thai_language_id):
    """Sauvegarde une carte dans la base de données avec téléchargement d'image"""
    try:
        # Récupérer ou créer l'expansion
        expansion_id = get_or_create_expansion(
            connection,
            card_details['set_code'],
            card_details.get('set_name_en')
        )

        # Sauvegarder directement dans pokemon_cards (structure unifiée)
        cursor = connection.cursor()

        # Vérifier si la carte existe déjà via card_versions
        thai_language_id = get_language_id(connection, 'th')
        card_already_exists = card_exists_in_db(connection, card_details.get('web_id'), thai_language_id)

        if card_already_exists:
            print(f"   🔍 Carte {card_details.get('web_id')} existe déjà - vérification des données manquantes...")
            # Télécharger l'image si elle n'existe pas (comme le scraper indonésien)
            if card_details.get('image_url'):
                set_code = card_details['set_code']
                image_filename = generate_image_filename(card_details, set_code)
                local_image_path = f"../images/{set_code}_TH/{image_filename}"

                # Vérifier si l'image existe déjà localement
                if not os.path.exists(local_image_path):
                    print(f"   📸 Image manquante - téléchargement...")
                    if download_image(card_details['image_url'], local_image_path):
                        print(f"   ✅ Image thaï téléchargée: {local_image_path}")
                    else:
                        print(f"   ❌ Échec téléchargement image: {card_details['image_url']}")
                else:
                    print(f"   ✅ Image déjà présente: {local_image_path}")
            return False

        # Télécharger l'image si disponible (comme le scraper indonésien)
        local_image_path = None
        if card_details.get('image_url'):
            set_code = card_details['set_code']
            image_filename = generate_image_filename(card_details, set_code)
            # Chemin relatif (comme l'indonésien)
            local_image_path = f"../images/{set_code}_TH/{image_filename}"

            print(f"   📥 Téléchargement de l'image thaïlandaise...")
            if download_image(card_details['image_url'], local_image_path):
                print(f"   ✅ Image thaï téléchargée: {local_image_path}")
            else:
                print(f"   ❌ Échec du téléchargement de l'image thaïlandaise")
                local_image_path = None

        # Gérer l'illustrateur si disponible
        illustrator_id = None
        if card_details.get('illustrator'):
            illustrator_id = get_or_create_illustrator(connection, card_details['illustrator'])

        # 1. Créer la carte de base dans pokemon_cards
        cursor.execute("""
            INSERT INTO pokemon_cards (
                expansion_id, card_number, illustrator_id, source, created_at, updated_at
            ) VALUES (%s, %s, %s, %s, NOW(), NOW())
        """, (
            expansion_id,
            card_details['card_number'],
            illustrator_id,
            'thai'
        ))

        card_id = cursor.lastrowid

        # 2. Créer la version thaï dans card_versions
        thai_language_id = get_language_id(connection, 'th')
        cursor.execute("""
            INSERT INTO card_versions (
                card_id, language_id, name, image_url, web_id, source, created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            card_id,
            thai_language_id,
            card_details['name'],
            local_image_path or card_details.get('image_url', ''),
            card_details.get('web_id', 0),
            'thai'
        ))

        connection.commit()
        card_id = cursor.lastrowid
        print(f"      ✅ Carte thaï créée avec ID: {card_id}")
        return card_id

    except Exception as e:
        print(f"      ❌ Erreur sauvegarde: {e}")
        connection.rollback()
        return None

def get_or_create_expansion(connection, set_code, set_name_en=None):
    """Récupère ou crée une expansion dans la nouvelle table expansions"""

    cursor = connection.cursor(dictionary=True)

    # Vérifier si l'expansion existe déjà
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    existing_expansion = cursor.fetchone()

    if existing_expansion:
        return existing_expansion['id']

    # Créer la nouvelle expansion
    print(f"🆕 Création de la nouvelle expansion: {set_code} - {set_name_en}")

    cursor.execute("""
        INSERT INTO expansions (code, name_en, region)
        VALUES (%s, %s, 'Thailand')
    """, (set_code, set_name_en))

    connection.commit()
    return cursor.lastrowid

def get_language_id(connection, language_code):
    """Récupère l'ID de la langue, la crée si elle n'existe pas"""
    cursor = connection.cursor(dictionary=True)
    cursor.execute("SELECT id FROM languages WHERE code = %s", (language_code,))
    result = cursor.fetchone()

    if result:
        return result['id']

    # Créer automatiquement la langue si elle n'existe pas
    print(f"⚠️  Langue {language_code} non trouvée, création automatique...")

    language_names = {
        'th': 'ไทย (Thai)',
        'id': 'Bahasa Indonesia',
        'en': 'English',
        'jp': '日本語 (Japanese)',
        'kr': '한국어 (Korean)'
    }

    language_name = language_names.get(language_code, language_code.upper())

    cursor.execute("""
        INSERT INTO languages (code, name)
        VALUES (%s, %s)
    """, (language_code, language_name))

    connection.commit()
    language_id = cursor.lastrowid

    print(f"✅ Langue {language_code} créée avec ID: {language_id}")
    return language_id

def card_exists_in_db(connection, web_id, language_id):
    """Vérifie si une carte existe déjà en base pour cette langue"""
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id FROM card_versions
        WHERE web_id = %s AND language_id = %s
    """, (str(web_id), language_id))
    result = cursor.fetchone()
    cursor.close()
    return result is not None

# Fonction supprimée - utilisation directe de pokemon_cards maintenant

def main():
    """Fonction principale - Scraping complet thaï"""
    
    parser = argparse.ArgumentParser(description='Scraper Thaï Pokemon TCG')
    parser.add_argument('--test', action='store_true', help='Mode test (limite à 100 IDs)')
    args = parser.parse_args()
    
    print("🇹🇭 SCRAPER THAÏ POKEMON TCG")
    print("=" * 60)
    print("🎯 Objectif: Récupérer TOUTES les cartes depuis le site thaï")
    print("📊 Source: asia.pokemon-card.com/th/")
    print("=" * 60)
    
    # Connexion base de données
    try:
        connection = mysql.connector.connect(**get_db_config())
        print("✅ Connexion à la base de données établie")
    except Exception as e:
        print(f"❌ Erreur connexion base: {e}")
        return
    
    # Scraper toutes les cartes avec sauvegarde progressive
    if args.test:
        print("🧪 Mode test activé - limitation à 100 IDs")
        # Pour le test, on peut limiter la portée

    # NOUVELLE MÉTHODE: Sauvegarde progressive
    saved_count = scrape_all_thai_cards_progressive(connection)

    if saved_count == 0:
        print("❌ Aucune nouvelle carte sauvegardée")
    else:
        print(f"✅ {saved_count} nouvelles cartes sauvegardées avec succès")

    connection.close()

    print(f"\n" + "=" * 60)
    print("🎉 SCRAPING THAÏ TERMINÉ")
    print("=" * 60)
    print(f"✅ {saved_count} nouvelles cartes sauvegardées")
    print("✅ Sauvegarde progressive - Aucune perte de données possible")
    print("✅ Toutes les données thaï sauvegardées avec VRAIS sets")

if __name__ == "__main__":
    main()
