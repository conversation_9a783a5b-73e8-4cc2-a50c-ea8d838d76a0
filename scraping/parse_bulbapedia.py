#!/usr/bin/env python3

import requests
from bs4 import BeautifulSoup
import json
import re
from urllib.parse import urljoin
import time
import random
import logging
import mysql.connector
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bulbapedia_scraper.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Configuration
BASE_URL = "https://bulbapedia.bulbagarden.net"
SETS_LIST_URL = "https://bulbapedia.bulbagarden.net/wiki/List_of_Pok%C3%A9mon_Trading_Card_Game_expansions"

def get_page_content(url, max_retries=3):
    """Récupère le contenu d'une page avec gestion des erreurs et retry."""
    for attempt in range(max_retries):
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            return response.content
        except requests.exceptions.RequestException as e:
            print(f"Erreur lors de la récupération de {url} (tentative {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(random.uniform(2, 5))
            else:
                return None

def get_all_expansion_links():
    """Récupère tous les liens vers les extensions depuis la page principale."""
    print("Récupération de la liste des extensions depuis Bulbapedia...")

    content = get_page_content(SETS_LIST_URL)
    if not content:
        print("❌ Impossible de récupérer la page principale")
        return []

    soup = BeautifulSoup(content, 'html.parser')
    expansion_links = []

    print("🔍 Recherche dans tous les tableaux...")

    # Chercher tous les liens dans les tableaux
    tables_found = soup.find_all('table', class_='wikitable')
    print(f"📋 {len(tables_found)} tableaux wikitable trouvés")

    for i, table in enumerate(tables_found):
        links_in_table = table.find_all('a')
        print(f"  Tableau {i+1}: {len(links_in_table)} liens")

        for link in links_in_table:
            href = link.get('href', '')
            title = link.get('title', '')
            text = link.get_text(strip=True)

            # Filtrer les liens qui semblent être des extensions TCG
            if (href and '/wiki/' in href and
                ('(TCG)' in title or '(TCG)' in text or
                 'expansion' in title.lower() or 'set' in title.lower()) and
                'List_of' not in href and 'Category:' not in href and
                'Template:' not in href and 'File:' not in href):

                full_url = urljoin(BASE_URL, href)
                expansion_name = title or text

                # Éviter les doublons
                if not any(exp['url'] == full_url for exp in expansion_links):
                    expansion_links.append({
                        'name': expansion_name,
                        'url': full_url
                    })
                    print(f"    ✅ Extension trouvée: {expansion_name}")

    # Si aucune extension trouvée, essayer une approche alternative
    if not expansion_links:
        print("🔄 Aucune extension trouvée avec la méthode principale, essai alternatif...")

        # Chercher tous les liens contenant "(TCG)"
        all_links = soup.find_all('a')
        print(f"🔍 Analyse de {len(all_links)} liens au total...")

        for link in all_links:
            href = link.get('href', '')
            title = link.get('title', '')
            text = link.get_text(strip=True)

            if (href and '/wiki/' in href and
                ('(TCG)' in title or '(TCG)' in text) and
                'List_of' not in href and 'Category:' not in href):

                full_url = urljoin(BASE_URL, href)
                expansion_name = title or text

                if not any(exp['url'] == full_url for exp in expansion_links):
                    expansion_links.append({
                        'name': expansion_name,
                        'url': full_url
                    })

    print(f"✅ Trouvé {len(expansion_links)} extensions sur Bulbapedia")

    # Afficher les premières extensions trouvées
    if expansion_links:
        print("📋 Premières extensions:")
        for i, exp in enumerate(expansion_links[:10]):
            print(f"  {i+1}. {exp['name']}")
        if len(expansion_links) > 10:
            print(f"  ... et {len(expansion_links) - 10} autres")

    return expansion_links

def clean_header(header):
    """Nettoie et normalise les en-têtes de tableau."""
    if not header:
        return ""

    # Supprimer les caractères spéciaux et normaliser
    header = re.sub(r'[^\w\s]', '', header.lower())
    header = re.sub(r'\s+', ' ', header).strip()

    # Mapper les en-têtes courants
    header_mapping = {
        'no': 'number',
        'card no': 'number',
        'card number': 'number',
        'card name': 'name',
        'pokemon': 'name',
        'pokmon': 'name',
        'card type': 'type',
        'pokemon type': 'type',
        'pokmon type': 'type',
        'card rarity': 'rarity',
        'pokemon rarity': 'rarity',
        'pokmon rarity': 'rarity',
        'img': 'image',
        'picture': 'image',
        'photo': 'image'
    }

    return header_mapping.get(header, header)

def is_card_table(headers):
    """Détermine si un tableau contient des cartes basé sur ses en-têtes."""
    required_headers = ['number', 'name']
    return any(header in headers for header in required_headers)

def extract_set_code(url, title):
    """Extrait le code de set depuis l'URL ou le titre."""
    # Essayer d'extraire depuis l'URL
    url_parts = url.split('/')
    if url_parts:
        last_part = url_parts[-1]
        # Supprimer les suffixes courants
        for suffix in ['_(TCG)', '_(set)', '_(expansion)']:
            if last_part.endswith(suffix):
                last_part = last_part[:-len(suffix)]
                break
        return last_part

    # Fallback sur le titre
    if title:
        # Supprimer les suffixes courants du titre
        for suffix in [' (TCG)', ' (set)', ' (expansion)']:
            if title.endswith(suffix):
                title = title[:-len(suffix)]
                break
        return title

    return "UNKNOWN"

def parse_card_table(table, set_url="", set_title=""):
    """Parse a card table and return a list of cards."""
    cards = []

    # Extraire le code de set
    set_code = extract_set_code(set_url, set_title)
    print(f"Code de set extrait : {set_code}")

    # Trouver les en-têtes
    header_row = table.find('tr')
    if not header_row:
        return cards

    # Extraire et nettoyer les en-têtes
    headers = []
    for th in header_row.find_all(['th', 'td']):
        header_text = clean_header(th.get_text(strip=True))
        if header_text and header_text not in headers:  # Éviter les doublons
            headers.append(header_text)

    print(f"En-têtes trouvés : {headers}")

    # Vérifier si c'est un tableau de cartes
    if not is_card_table(headers):
        print("Ce n'est pas un tableau de cartes")
        return cards

    # Parcourir les lignes du tableau (en sautant l'en-tête)
    for row in table.find_all('tr')[1:]:
        cells = row.find_all(['td', 'th'])
        if not cells or len(cells) < len(headers):
            continue

        # Créer un dictionnaire pour stocker les données de la carte
        card_data = {
            'set_code': set_code,
            'is_numbered': True  # Par défaut, on considère que la carte est numérotée
        }

        # Parcourir les cellules et les associer aux en-têtes
        for i, cell in enumerate(cells):
            if i >= len(headers):
                break

            header = headers[i]
            if not header:  # Ignorer les colonnes sans en-tête
                continue

            # Extraire le texte de la cellule
            value = cell.get_text(strip=True)
            if not value:  # Ignorer les cellules vides
                continue

            # Pour les colonnes spéciales, extraire plus d'informations
            if header == 'name':
                # Chercher le lien pour l'URL de la carte
                link = cell.find('a')
                if link and link.get('href'):
                    card_data['url'] = f"https://bulbapedia.bulbagarden.net{link.get('href')}"
                # Extraire le nom de la carte
                card_data[header] = value
            elif header == 'number':
                # Nettoyer le numéro de carte
                value = value.replace('No.', '').strip()
                # Vérifier si la carte est numérotée
                if value == '—' or not value:
                    card_data['is_numbered'] = False
                card_data[header] = value
            elif header == 'type':
                # Nettoyer le type
                value = value.replace('Type', '').strip()
                card_data[header] = value
            elif header == 'rarity':
                # Nettoyer la rareté
                value = value.replace('Rarity', '').strip()
                card_data[header] = value
            elif header == 'image':
                # Extraire l'URL de l'image
                img = cell.find('img')
                if img and img.get('src'):
                    card_data[header] = urljoin('https:', img.get('src'))
            else:
                card_data[header] = value

        # Ajouter la carte si elle a au moins un nom
        if 'name' in card_data:
            cards.append(card_data)
            print(f"  → Carte trouvée : {card_data['name']} ({card_data.get('number', 'Non numérotée')})")

    return cards

def extract_language_versions(soup):
    """Extrait les versions linguistiques d'un set à partir de la section 'In other languages'."""
    language_versions = {}

    # Chercher la section "In other languages"
    language_section = None
    for h2 in soup.find_all('h2'):
        if 'in other languages' in h2.text.lower():
            language_section = h2
            break

    if language_section:
        # Trouver le tableau suivant
        table = language_section.find_next('table')
        if table:
            # Parcourir les lignes du tableau (en sautant l'en-tête)
            for row in table.find_all('tr')[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    language = cells[0].get_text(strip=True)
                    title = cells[1].get_text(strip=True)

                    # Gérer les cas spéciaux comme le chinois
                    if language == 'Chinese':
                        # Chercher les sous-versions (Cantonese, Mandarin)
                        for sub_row in row.find_next_siblings('tr'):
                            sub_cells = sub_row.find_all(['td', 'th'])
                            if len(sub_cells) >= 2:
                                sub_lang = sub_cells[0].get_text(strip=True)
                                sub_title = sub_cells[1].get_text(strip=True)
                                language_versions[f"Chinese ({sub_lang})"] = sub_title
                    else:
                        language_versions[language] = title

    return language_versions

def parse_expansion_page(url):
    """Parse une page d'extension et retourne les cartes trouvées."""
    content = get_page_content(url)
    if not content:
        return [], None

    soup = BeautifulSoup(content, 'html.parser')
    cards = []

    # Extraire le titre de la page pour le code de set
    title = soup.find('h1', {'id': 'firstHeading'})
    set_title = title.get_text(strip=True) if title else ""

    # Extraire les versions linguistiques
    language_versions = extract_language_versions(soup)
    print(f"\nVersions linguistiques trouvées pour {set_title}:")
    for lang, title in language_versions.items():
        print(f"  {lang}: {title}")

    # Extraire le nom anglais du set
    english_name = None
    if 'english' in language_versions:
        english_name = language_versions['english']
    elif 'en' in language_versions:
        english_name = language_versions['en']

    # Trouver toutes les tables
    tables = soup.find_all('table')
    for table in tables:
        # Vérifier si c'est une table de cartes
        if table.find('tr'):
            table_cards = parse_card_table(table, url, set_title)
            # Ajouter les informations de version linguistique à chaque carte
            for card in table_cards:
                card['language_versions'] = language_versions
            cards.extend(table_cards)

    return cards, english_name

def load_cards():
    """Charge les cartes existantes depuis le fichier JSON."""
    try:
        with open('bulbapedia_all_cards.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def save_cards(cards):
    """Sauvegarde les cartes dans le fichier JSON."""
    with open('bulbapedia_all_cards.json', 'w', encoding='utf-8') as f:
        json.dump(cards, f, ensure_ascii=False, indent=2)

def update_cards(existing_cards, new_cards):
    """Met à jour les cartes existantes avec les nouvelles cartes."""
    for card in new_cards:
        # Créer une clé unique pour chaque carte
        card_key = f"{card['set_code']}_{card.get('number', 'unnumbered')}_{card['name']}"
        existing_cards[card_key] = card
    return existing_cards

def save_to_database(cards):
    """Sauvegarde les cartes en base de données MySQL."""
    if not cards:
        return

    try:
        # Connexion à la base de données
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor()

        for card_key, card in cards.items():
            try:
                # Préparer les données pour l'insertion
                card_number = card.get('number', '')
                name_en = card.get('name', '')
                card_type = card.get('type', '')
                rarity = card.get('rarity', '')
                set_code = card.get('set_code', '')
                source_url = card.get('url', '')

                # Requête d'insertion avec gestion des doublons
                query = """
                INSERT INTO pokemon_cards (card_number, name_en, card_type, source, card_url)
                VALUES (%s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                name_en = VALUES(name_en),
                card_type = VALUES(card_type),
                source = VALUES(source),
                card_url = VALUES(card_url)
                """

                cursor.execute(query, (card_number, name_en, card_type, 'bulbapedia', source_url))

            except mysql.connector.Error as e:
                print(f"Erreur lors de l'insertion de la carte {card.get('name', 'Unknown')}: {e}")
                continue

        # Valider les changements
        connection.commit()
        print(f"✅ {len(cards)} cartes sauvegardées en base de données")

    except mysql.connector.Error as e:
        print(f"❌ Erreur de connexion à la base de données: {e}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """Fonction principale pour scraper toutes les cartes de Bulbapedia."""
    print("🌐 DÉMARRAGE DU SCRAPER BULBAPEDIA COMPLET")
    print("🎯 Objectif: Récupérer TOUS les sets et cartes depuis Bulbapedia")
    print("🔥 SCRAPING COMPLET - AUCUNE LIMITATION")
    print("=" * 60)

    # Charger les cartes existantes
    existing_cards = load_cards()
    print(f"Cartes existantes chargées: {len(existing_cards)}")

    # Récupérer toutes les extensions
    all_expansions = get_all_expansion_links()
    if not all_expansions:
        print("❌ Aucune extension trouvée")
        return

    print(f"📋 {len(all_expansions)} extensions à traiter")

    # Connexion à la base de données
    try:
        connection = mysql.connector.connect(**get_db_config())
        print("✅ Connexion à la base de données établie")
        connection.close()
    except Exception as e:
        print(f"⚠️  Avertissement connexion base: {e}")
        print("📝 Les données seront sauvegardées uniquement en JSON")

    set_english_names = {}

    # Traiter chaque extension
    for i, expansion in enumerate(all_expansions, 1):
        print(f"\n🎯 EXTENSION {i}/{len(all_expansions)}: {expansion['name']}")
        print(f"📡 URL: {expansion['url']}")

        # Parser la page de l'extension
        new_cards, english_name = parse_expansion_page(expansion['url'])

        if new_cards:
            print(f"✅ {len(new_cards)} cartes trouvées")

            # Sauvegarder en base de données
            save_to_database(new_cards)

            # Mettre à jour les cartes et sauvegarder régulièrement
            updated_cards = update_cards(existing_cards, new_cards)
            save_cards(updated_cards)
            existing_cards = updated_cards

            # Sauvegarder le nom anglais si disponible
            if english_name:
                set_english_names[expansion['name']] = english_name
        else:
            print("❌ Aucune carte trouvée")

        # Pause entre les requêtes pour éviter de surcharger le serveur
        time.sleep(random.uniform(1.0, 3.0))

    # Sauvegarder les noms anglais des sets
    with open('set_english_names.json', 'w', encoding='utf-8') as f:
        json.dump(set_english_names, f, ensure_ascii=False, indent=2)

    print(f"\n" + "=" * 60)
    print("🎉 SCRAPING BULBAPEDIA TERMINÉ")
    print("=" * 60)
    print(f"✅ Nombre total de cartes : {len(existing_cards)}")
    print("✅ Sauvegarde des résultats dans bulbapedia_all_cards.json")
    print("✅ Sauvegarde des noms anglais des sets dans set_english_names.json")

if __name__ == '__main__':
    main()
