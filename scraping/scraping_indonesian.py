#!/usr/bin/env python3
"""
Scraper pour récupérer les noms indonésiens des cartes Pokemon TCG
depuis le site officiel indonésien : https://asia.pokemon-card.com/id/card-search/list/

Basé sur scraping_thai.py avec adaptations pour l'indonésien.
"""

import requests
from bs4 import BeautifulSoup
import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config
import time
import random
import logging
import argparse
import os
import re
from urllib.parse import urljoin, urlparse

# Configuration du logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# URLs du site indonésien
BASE_URL = "https://asia.pokemon-card.com"
LIST_URL = "https://asia.pokemon-card.com/id/card-search/list/?expansionCodes="
PROGRESS_FILE = "scraping_indonesian_progress.txt"

# DÉCOUVERTE AUTOMATIQUE DE TOUS LES SETS DANS L'ORDRE DU SITE !
def discover_all_indonesian_sets():
    """Découvre automatiquement TOUS les sets disponibles sur le site indonésien DANS L'ORDRE DU SITE"""
    print("🔍 DÉCOUVERTE AUTOMATIQUE DE TOUS LES SETS INDONÉSIENS...")
    print("📋 Analyse de la structure du site indonésien...")

    # Méthode 1: Essayer de découvrir via les URLs de sets connus
    print("🔍 Méthode 1: Test des sets récents...")

    # Liste des sets récents à tester (ordre inverse = plus récents en premier)
    test_sets = [
        'SV10s', 'SV9s', 'SV8s', 'SV8a', 'SVM', 'SV7s', 'SV6s', 'SV5s', 'SV4s', 'SV4a',
        'SV3s', 'SV3a', 'SV2s', 'SV2a', 'SV1s', 'SV1a',
        'S12a', 'S12', 'S11a', 'S11', 'S10a', 'S10b', 'S10', 'S9a', 'S9', 'S8b', 'S8a', 'S8',
        'S7R', 'S7D', 'S6a', 'S6K', 'S6H', 'S5a', 'S5R', 'S5I', 'S4a', 'S4', 'S3a', 'S3'
    ]

    available_sets = []

    for set_code in test_sets:
        test_url = f"https://asia.pokemon-card.com/id/card-search/list/?expansionCodes={set_code}"
        print(f"   🔍 Test {set_code}...", end="")

        try:
            response = safe_request(test_url)
            if response and response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Chercher s'il y a des cartes (pas de message "0 buah")
                result_text = soup.get_text()
                if "0 buah" not in result_text and "buah" in result_text:
                    available_sets.append(set_code)
                    print(" ✅")
                else:
                    print(" ❌")
            else:
                print(" ❌")
        except:
            print(" ❌")

        # Petit délai pour éviter de surcharger le serveur
        time.sleep(0.5)

    if available_sets:
        print(f"\n✅ {len(available_sets)} sets découverts automatiquement !")
        for i, set_code in enumerate(available_sets):
            print(f"   {i+1}. {set_code}")
        print("🎯 Le scraper va traiter TOUS ces sets dans CET ORDRE !")
        return available_sets
    else:
        print("❌ AUCUN SET DÉCOUVERT AUTOMATIQUEMENT")
        print("❌ Le scraper doit détecter les vrais sets depuis le site, pas utiliser une liste hardcodée !")
        print("❌ Vérifiez la connectivité ou la structure HTML du site indonésien")
        return []



def safe_request(url, max_retries=3, delay=2):
    """Effectue une requête HTTP sécurisée avec retry"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            logging.warning(f"Tentative {attempt + 1}/{max_retries} échouée pour {url}: {e}")
            if attempt < max_retries - 1:
                time.sleep(delay * (attempt + 1))
            else:
                logging.error(f"Échec définitif pour {url} après {max_retries} tentatives")
                return None

def get_set_pages(set_code):
    """Détermine le nombre de pages pour un set spécifique"""
    logging.info(f"Détection du nombre de pages pour le set {set_code}...")

    # Tester la première page pour obtenir le total
    test_url = f"https://asia.pokemon-card.com/id/card-search/list/?expansionCodes={set_code}"

    response = safe_request(test_url)
    if not response:
        logging.error(f"Impossible d'accéder au set {set_code}")
        return 0

    soup = BeautifulSoup(response.content, 'html.parser')

    # DEBUG: Chercher tous les textes avec "halaman"
    all_halaman_texts = soup.find_all(string=re.compile(r'halaman'))
    logging.debug(f"Textes avec 'halaman' trouvés: {[t.strip() for t in all_halaman_texts]}")

    # Chercher le texte "/ Total X halaman"
    page_info = soup.find(string=re.compile(r'Total \d+ halaman'))
    if page_info:
        match = re.search(r'Total (\d+) halaman', page_info)
        if match:
            total_pages = int(match.group(1))
            logging.info(f"Set {set_code}: {total_pages} pages trouvées")
            return total_pages
    else:
        logging.debug(f"Aucun texte 'Total X halaman' trouvé pour {set_code}")

    # Fallback: chercher les liens de pagination
    pagination = soup.find_all('a', href=re.compile(r'pageNo=\d+'))
    if pagination:
        max_page = 1
        for link in pagination:
            href = link.get('href', '')
            page_match = re.search(r'pageNo=(\d+)', href)
            if page_match:
                page_num = int(page_match.group(1))
                max_page = max(max_page, page_num)

        logging.info(f"Set {set_code}: {max_page} pages trouvées (via pagination)")
        return max_page

    logging.warning(f"Set {set_code}: Impossible de déterminer le nombre de pages, utilisation de 1")
    return 1

def save_progress(page):
    """Sauvegarde la progression"""
    with open(PROGRESS_FILE, 'w') as f:
        f.write(str(page))
    logging.info(f"Progression sauvegardée: Page {page} traitée.")

def read_progress():
    """Lit la progression sauvegardée"""
    if os.path.exists(PROGRESS_FILE):
        try:
            with open(PROGRESS_FILE, 'r') as f:
                page = int(f.read().strip())
                logging.info(f"Progression trouvée: Dernière page traitée {page}.")
                return page
        except (ValueError, IOError) as e:
            logging.warning(f"Erreur lors de la lecture de la progression: {e}")
    else:
        logging.info(f"{PROGRESS_FILE} non trouvé. Démarrage du scraping depuis le début.")
    return None

def extract_real_set_info_indonesian(soup):
    """Extrait les VRAIES informations du set depuis la page indonésienne"""

    set_info = {
        'code': None,
        'name_id': None,  # nom indonésien
        'name_en': None
    }

    try:
        # Chercher le lien vers le set (méthode principale)
        set_links = soup.find_all('a', href=re.compile(r'expansionCodes='))

        for link in set_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)

            # Extraire le code du set depuis l'URL
            code_match = re.search(r'expansionCodes=([^&]+)', href)
            if code_match:
                set_info['code'] = code_match.group(1)
                set_info['name_id'] = text  # nom indonésien

                # Essayer de deviner le nom anglais
                if 'starter' in text.lower():
                    set_info['name_en'] = f"Starter Deck {set_info['code']}"
                elif 'sv' in set_info['code'].lower():
                    set_info['name_en'] = f"Scarlet & Violet {set_info['code']}"
                else:
                    set_info['name_en'] = f"Set {set_info['code']}"

                break

        # Fallback: chercher dans le texte de la page
        if not set_info['code']:
            page_text = soup.get_text()

            # Patterns de codes de sets
            code_patterns = [
                r'\b(SV\d+[a-z]*)\b',    # SV1s, SV2a, etc.
                r'\b(S\d+[a-z]*)\b',     # S1, S2a, etc.
            ]

            for pattern in code_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    set_info['code'] = matches[0].upper()
                    set_info['name_en'] = f"Set {set_info['code']}"
                    break

    except Exception as e:
        logging.error(f"Erreur extraction set info indonésien: {e}")

    return set_info

def scrape_card_details(card_url, max_retries=3):
    """Scrape les détails d'une carte depuis son URL"""

    for attempt in range(max_retries):
        try:
            response = safe_request(card_url)
            if not response:
                continue

            soup = BeautifulSoup(response.content, 'html.parser')

            card_details = {}

            # Extraire les informations du set RÉELLES
            set_info = extract_real_set_info_indonesian(soup)
            card_details['set_info'] = set_info

            # Extraire le nom indonésien
            h1_tag = soup.find('h1', attrs={'class': 'pageHeader'})
            if h1_tag:
                span_tag = h1_tag.find('span')
                if span_tag:
                    span_tag.decompose()
                card_details['nameCard'] = h1_tag.get_text(strip=True)
            else:
                card_details['nameCard'] = None

            # Extraire le numéro de carte
            number_element = soup.find('span', string=re.compile(r'\d+/\d+'))
            if number_element:
                card_details['numberCard'] = number_element.get_text(strip=True)
            else:
                card_details['numberCard'] = None
            
            # Extraire l'URL de l'image (chercher l'image de la carte)
            # L'image de la carte a une URL qui contient '/card-img/' et l'ID de la carte
            img_tag = soup.find('img', src=re.compile(r'/card-img/id\d+\.png'))
            if img_tag and img_tag.get('src'):
                card_details['imageUrl'] = urljoin(BASE_URL, img_tag['src'])
            else:
                # Fallback: chercher toute image avec /card-img/ dans l'URL
                img_tag = soup.find('img', src=re.compile(r'/card-img/'))
                if img_tag and img_tag.get('src'):
                    card_details['imageUrl'] = urljoin(BASE_URL, img_tag['src'])
                else:
                    card_details['imageUrl'] = None
            
            # Extraire l'ID web depuis l'URL
            url_parts = card_url.rstrip('/').split('/')
            if len(url_parts) >= 2 and url_parts[-1].isdigit():
                card_details['webId'] = int(url_parts[-1])
            else:
                card_details['webId'] = None
            
            return card_details
            
        except Exception as e:
            logging.warning(f"Tentative {attempt + 1}/{max_retries} échouée pour {card_url}: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
    
    logging.error(f"Échec définitif du scraping pour {card_url}")
    return None

def download_image(image_url, local_path, max_retries=3):
    """Télécharge une image depuis une URL"""
    
    if os.path.exists(local_path):
        logging.info(f"L'image existe déjà: {local_path}")
        return True
    
    # Créer le dossier si nécessaire
    os.makedirs(os.path.dirname(local_path), exist_ok=True)
    
    for attempt in range(max_retries):
        try:
            response = safe_request(image_url)
            if response:
                with open(local_path, 'wb') as f:
                    f.write(response.content)
                logging.info(f"Image téléchargée: {local_path}")
                return True
        except Exception as e:
            logging.warning(f"Tentative {attempt + 1}/{max_retries} échouée pour {image_url}: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
    
    logging.error(f"Échec du téléchargement: {image_url}")
    return False

# Fonction supprimée - vérification simplifiée dans create_card_post()

def create_card_post(connection, card_data, current_set_code, force_update=False):
    """Crée une carte dans le système unifié pokemon_cards + card_versions"""

    if not card_data.get('webId'):
        logging.warning("Aucun webId trouvé pour cette carte")
        return None

    web_id = card_data['webId']
    set_code = extract_set_code_from_url_or_card(card_data, current_set_code)

    # Vérifier si la carte existe déjà via card_versions
    indonesian_language_id = get_language_id(connection, 'id')
    if card_exists_in_db(connection, str(web_id), indonesian_language_id):
        print(f"   🔍 Carte {web_id} existe déjà - vérification des données manquantes...")

        # Vérifier si l'image manque et la télécharger si nécessaire
        if card_data.get('imageUrl'):
            image_filename = generate_image_filename(card_data, set_code)
            local_image_path = f"../images/{set_code}/{image_filename}"

            # Vérifier si l'image existe déjà localement
            if not os.path.exists(local_image_path):
                print(f"   📸 Image manquante - téléchargement...")
                if download_image(card_data['imageUrl'], local_image_path):
                    print(f"   ✅ Image indonésienne téléchargée: {local_image_path}")
                else:
                    print(f"   ❌ Échec téléchargement image: {card_data['imageUrl']}")
            else:
                print(f"   ✅ Image déjà présente: {local_image_path}")

            # IMPORTANT: Mettre à jour l'image_url ET local_image_path en base de données
            image_filename = generate_image_filename(card_data, set_code)
            local_image_path = f"images/{image_filename}"

            cursor = connection.cursor()
            cursor.execute("""
                UPDATE card_versions
                SET image_url = %s, local_image_path = %s
                WHERE web_id = %s AND language_id = %s
            """, (card_data.get('imageUrl', ''), local_image_path, str(web_id), indonesian_language_id))
            connection.commit()
            print(f"   ✅ URL d'image et chemin local mis à jour: {local_image_path}")

        return None  # Carte déjà complète

    print(f"   ✅ Nouvelle carte indonésienne à créer")

    # Récupérer ou créer l'expansion
    expansion_id = get_or_create_expansion_new(connection, set_code, card_data.get('nameCardEn', f'Set {set_code}'))

    # Créer la carte maître dans pokemon_cards
    cursor = connection.cursor()
    cursor.execute("""
        INSERT INTO pokemon_cards (expansion_id, card_number, source)
        VALUES (%s, %s, %s)
    """, (expansion_id, card_data.get('numberCard', ''), 'indonesian'))

    card_id = cursor.lastrowid
    print(f"   ✅ Carte maître créée avec ID: {card_id}")

    # Générer le chemin local de l'image
    image_filename = generate_image_filename(card_data, set_code)
    local_image_path = f"images/{image_filename}"

    # Créer la version indonésienne dans card_versions
    cursor.execute("""
        INSERT INTO card_versions (card_id, language_id, name, rarity, web_id, source, image_url, local_image_path)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        card_id,
        indonesian_language_id,
        card_data.get('nameCard', ''),
        card_data.get('rarity', ''),
        str(web_id),
        'indonesian',
        card_data.get('imageUrl', ''),
        local_image_path
    ))

    print(f"   💾 {card_data.get('nameCard', 'Carte')} - Sauvegardée")

    # Télécharger l'image si disponible
    if card_data.get('imageUrl'):
        image_filename = generate_image_filename(card_data, set_code)
        local_image_path = f"../images/{set_code}/{image_filename}"

        if download_image(card_data['imageUrl'], local_image_path):
            print(f"   ✅ Image indonésienne téléchargée: {local_image_path}")
        else:
            print(f"   ❌ Échec téléchargement image: {card_data['imageUrl']}")

    connection.commit()
    cursor.close()

    return card_id



def extract_set_code_from_url_or_card(card_data, current_set_code=None):
    """Extrait le code du set depuis les données de la carte avec suffixe indonésien _I"""

    # Si on a le set en cours, l'utiliser directement avec suffixe _I
    if current_set_code:
        return f"{current_set_code.upper()}_I"

    # Fallback : essayer d'extraire depuis l'URL si disponible
    if 'url' in card_data:
        url = card_data['url']
        # Extraire le set code depuis l'URL si possible
        # Format: https://asia.pokemon-card.com/id/card-search/detail/15861/
        pass

    # Mapping des IDs indonésiens vers les codes de sets (basé sur les ranges observés)
    web_id = card_data.get('webId', 0)

    # Ranges d'IDs pour les sets indonésiens avec suffixe _I (FALLBACK SEULEMENT)
    if web_id >= 15861:  # SV10s commence à 15861
        return 'SV10S_I'
    elif web_id >= 15000:  # SV9s
        return 'SV9S_I'
    elif web_id >= 14000:  # SV8a ou autres
        return 'SV8A_I'
    elif web_id >= 13000:  # SVM
        return 'SVM_I'
    elif web_id >= 12000:  # SV8s
        return 'SV8S_I'
    elif web_id >= 11000:  # SV7s
        return 'SV7S_I'
    elif web_id >= 10000:  # SV6s
        return 'SV6S_I'
    elif web_id >= 9900:   # SV4a (Harta Berkilau)
        return 'SV4A_I'
    else:
        # Pour les IDs plus anciens, utiliser un format générique avec suffixe _I
        return f'SET_{web_id // 1000}K_I'

def generate_image_filename(card_data, set_code):
    """Génère le nom de fichier pour l'image (format simple comme les images thaï)"""
    card_number = card_data.get('numberCard', '000')

    # Vérifier que card_number n'est pas None
    if card_number is None:
        card_number = '000'

    # Nettoyer le numéro de carte (prendre seulement la partie avant le /)
    if '/' in card_number:
        clean_number = card_number.split('/')[0].zfill(3)
        total_cards = card_number.split('/')[1].zfill(3)
    else:
        # Si pas de format X/Y, utiliser le numéro tel quel
        clean_number = str(card_number).zfill(3)
        total_cards = "999"  # Valeur par défaut

    return f"{clean_number}_{total_cards}.jpg"

def parse_arguments():
    """Parse les arguments de ligne de commande"""
    parser = argparse.ArgumentParser(description='Scraper pour les noms indonésiens des cartes Pokemon TCG')
    parser.add_argument('--max-sets', type=int, default=None,
                      help='Nombre maximum de sets à traiter (pour les tests)')
    parser.add_argument('--min-delay', type=float, default=1.0,
                      help='Délai minimum entre les requêtes (secondes)')
    parser.add_argument('--max-delay', type=float, default=3.0,
                      help='Délai maximum entre les requêtes (secondes)')
    parser.add_argument('--max-retries', type=int, default=3,
                      help='Nombre maximum de tentatives par requête')
    parser.add_argument('--reset-progress', action='store_true',
                      help='Réinitialise la progression du scraping et recommence depuis le début.')
    parser.add_argument('--force-update', action='store_true',
                      help='Force la mise à jour des cartes même si elles existent déjà.')
    return parser.parse_args()




def get_or_create_expansion_new(connection, set_code, set_name_en=None, set_name_id=None):
    """Récupère ou crée une expansion dans la nouvelle table expansions"""
    cursor = connection.cursor(dictionary=True)

    # Vérifier si l'expansion existe déjà
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    existing_expansion = cursor.fetchone()

    if existing_expansion:
        print(f"   ✅ Set {set_code} existe déjà avec ID: {existing_expansion['id']}")
        return existing_expansion['id']

    print(f"   📦 Création nouveau set: {set_code}")
    if set_name_id:
        print(f"   🇮🇩 Nom indonésien: {set_name_id}")
    if set_name_en:
        print(f"   🇺🇸 Nom anglais: {set_name_en}")

    # Nom par défaut basé sur le code
    if not set_name_en:
        set_name_en = f"Set {set_code}"

    # Vérifier si la colonne name_id existe
    cursor.execute("SHOW COLUMNS FROM expansions")
    columns = [row['Field'] for row in cursor.fetchall()]

    if 'name_id' in columns and set_name_id:
        # Insérer avec nom indonésien
        cursor.execute("""
            INSERT INTO expansions (code, name_en, name_id, region)
            VALUES (%s, %s, %s, 'Indonesia')
        """, (set_code, set_name_en, set_name_id))
    else:
        # Insérer sans nom indonésien (colonne n'existe pas encore)
        cursor.execute("""
            INSERT INTO expansions (code, name_en, region)
            VALUES (%s, %s, 'Indonesia')
        """, (set_code, set_name_en))

    connection.commit()
    expansion_id = cursor.lastrowid
    print(f"   ✅ Set créé avec ID: {expansion_id}")

    return expansion_id

# Fonction supprimée - plus nécessaire avec pokemon_cards

# Fonction supprimée - plus nécessaire avec pokemon_cards

# Fonction supprimée - utiliser get_or_create_expansion_new() à la place

def card_exists_in_db(connection, web_id, language_id):
    """Vérifie si une carte existe déjà dans card_versions"""
    cursor = connection.cursor()
    cursor.execute("""
        SELECT COUNT(*) FROM card_versions
        WHERE web_id = %s AND language_id = %s
    """, (web_id, language_id))

    count = cursor.fetchone()[0]
    cursor.close()
    return count > 0

def get_language_id(connection, language_code):
    """Récupère l'ID de la langue, la crée si elle n'existe pas"""
    cursor = connection.cursor(dictionary=True)
    cursor.execute("SELECT id FROM languages WHERE code = %s", (language_code,))
    result = cursor.fetchone()

    if result:
        return result['id']

    # Créer automatiquement la langue si elle n'existe pas
    print(f"⚠️  Langue {language_code} non trouvée, création automatique...")

    language_names = {
        'id': 'Bahasa Indonesia',
        'en': 'English',
        'th': 'ไทย (Thai)',
        'jp': '日本語 (Japanese)',
        'kr': '한국어 (Korean)'
    }

    language_name = language_names.get(language_code, language_code.upper())

    cursor.execute("""
        INSERT INTO languages (code, name)
        VALUES (%s, %s)
    """, (language_code, language_name))

    connection.commit()
    language_id = cursor.lastrowid

    print(f"✅ Langue {language_code} créée avec ID: {language_id}")
    return language_id

def main():
    """Fonction principale"""
    print("🇮🇩 SCRAPER INDONÉSIEN POKEMON TCG")
    print("=" * 50)

    args = parse_arguments()

    if args.reset_progress and os.path.exists(PROGRESS_FILE):
        os.remove(PROGRESS_FILE)
        logging.info(f"{PROGRESS_FILE} supprimé. Le scraping recommencera depuis le début.")

    # Configuration de la base de données
    print("\n=== Configuration de la base de données ===")
    config = get_db_config()
    print(f"Host: {config['host']}")
    print(f"Database: {config['database']}")
    print(f"Port: {config['port']}")
    print(f"User: {config['user']}")

    try:
        connection = mysql.connector.connect(**config)
        print("✅ Connexion à la base de données réussie !")
    except mysql.connector.Error as e:
        print(f"❌ Erreur de connexion à la base de données: {e}")
        exit(1)

    try:
        # Vérifier que la colonne name_id existe
        cursor = connection.cursor()
        cursor.execute("SHOW COLUMNS FROM pokemon_cards LIKE 'name_id'")
        if not cursor.fetchone():
            print("❌ Colonne name_id manquante. Ajout en cours...")
            cursor.execute("ALTER TABLE pokemon_cards ADD COLUMN name_id VARCHAR(255) DEFAULT NULL")
            connection.commit()
            print("✅ Colonne name_id ajoutée")
        else:
            print("✅ Colonne name_id existe")
        cursor.close()

        print("\nTraitement des sets indonésiens...")

        # DÉCOUVERTE AUTOMATIQUE DE TOUS LES SETS DANS L'ORDRE DU SITE !
        indonesian_sets = discover_all_indonesian_sets()
        if not indonesian_sets:
            print("❌ Aucun set découvert ! Arrêt du scraper.")
            return

        # Traiter chaque set indonésien dans l'ordre du site
        sets_processed = 0

        for set_code in indonesian_sets:
            if args.max_sets and sets_processed >= args.max_sets:
                logging.info(f"Limite de {args.max_sets} set(s) atteinte. Arrêt du scraping.")
                break

            logging.info(f"🇮🇩 Traitement du set {set_code}...")

            # Déterminer le nombre de pages pour ce set
            total_pages = get_set_pages(set_code)
            if total_pages == 0:
                logging.warning(f"Set {set_code} ignoré (aucune page trouvée)")
                continue

            # Traiter toutes les pages de ce set
            for page_num in range(1, total_pages + 1):
                logging.info(f"Traitement du set {set_code}, page {page_num}/{total_pages}...")

                if page_num == 1:
                    url = f"https://asia.pokemon-card.com/id/card-search/list/?expansionCodes={set_code}"  # Première page sans pageNo
                else:
                    url = f"https://asia.pokemon-card.com/id/card-search/list/?pageNo={page_num}&expansionCodes={set_code}"

                response = safe_request(url)
                if not response:
                    logging.error(f"Erreur lors de la récupération de {set_code} page {page_num}")
                    continue

                soup = BeautifulSoup(response.content, 'html.parser')

                # Trouver tous les liens vers les cartes
                card_links = soup.find_all('a', href=re.compile(r'/id/card-search/detail/\d+/'))

                if not card_links:
                    logging.warning(f"Aucune carte trouvée sur {set_code} page {page_num}")
                    continue

                logging.info(f"Trouvé {len(card_links)} cartes sur {set_code} page {page_num}")

                for card_idx, card_link in enumerate(card_links):
                    card_url = urljoin(BASE_URL, card_link['href'])
                    logging.info(f"Traitement carte {card_idx + 1}/{len(card_links)}: {card_url}")

                    card_details = scrape_card_details(card_url, max_retries=args.max_retries)
                    if card_details:
                        card_db_id = create_card_post(connection, card_details, set_code, force_update=args.force_update)
                        if card_db_id:
                            logging.info(f"Carte ajoutée/mise à jour avec succès (ID DB: {card_db_id}, Web ID: {card_details.get('webId')})")
                        else:
                            logging.warning(f"Échec de l'ajout/mise à jour de la carte: {card_url}")
                    else:
                        logging.error(f"Échec du scraping des détails pour: {card_url}")

                    time.sleep(random.uniform(args.min_delay / 2, args.max_delay / 2))

                time.sleep(random.uniform(args.min_delay, args.max_delay))

            sets_processed += 1
            logging.info(f"Set {set_code} terminé ({sets_processed}/{len(indonesian_sets)})")

        logging.info("Scraping indonésien terminé avec succès!")

        # Le reste du code de la boucle n'est plus nécessaire
        return



    except Exception as e:
        logging.error(f"Erreur durant le scraping: {e}")
        raise
    finally:
        if connection:
            connection.close()
            print("Connexion à la base de données fermée")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Scraping interrompu par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        raise
