#!/usr/bin/env python3
"""
Scraper pour extraire les correspondances de sets depuis Bulbapedia
Crée le mapping entre les différentes régions/langues
"""

import requests
from bs4 import BeautifulSoup
import mysql.connector
import json
import re
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def create_set_mappings_table():
    """Crée la table de correspondances des sets"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor()
        
        # Créer la table de correspondances
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS set_mappings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                bulbapedia_name VARCHAR(255) NOT NULL,
                english_name VARCHAR(255),
                japanese_name VARCHAR(255),
                thai_name VARCHAR(255),
                indonesian_name VA<PERSON>HA<PERSON>(255),
                korean_name VA<PERSON>HA<PERSON>(255),
                chinese_traditional_name VARCHAR(255),
                chinese_simplified_name VARCHAR(255),
                english_code VARCHAR(50),
                japanese_code VARCHAR(50),
                thai_code VARCHAR(50),
                indonesian_code VARCHAR(50),
                korean_code VARCHAR(50),
                chinese_traditional_code VARCHAR(50),
                chinese_simplified_code VARCHAR(50),
                series VARCHAR(100),
                release_year INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_bulbapedia (bulbapedia_name),
                INDEX idx_english_name (english_name),
                INDEX idx_series (series)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        connection.commit()
        print("✅ Table set_mappings créée avec succès")
        
        return True
        
    except mysql.connector.Error as e:
        if "already exists" in str(e):
            print("✅ Table set_mappings existe déjà")
            return True
        else:
            print(f"❌ Erreur création table: {e}")
            return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def scrape_bulbapedia_expansions():
    """Scrape la page des expansions Bulbapedia"""
    
    url = "https://bulbapedia.bulbagarden.net/wiki/List_of_Pokémon_Trading_Card_Game_expansions"
    
    try:
        print("🔍 Scraping de la page Bulbapedia...")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extraire les correspondances par série
        mappings = []
        
        # Chercher les sections principales
        sections = [
            'Scarlet & Violet Series',
            'Sword & Shield Series', 
            'Sun & Moon Series',
            'XY Series',
            'Black & White Series'
        ]
        
        for section_name in sections:
            print(f"📋 Traitement de la série: {section_name}")
            
            # Trouver la section
            section_header = soup.find('span', {'id': section_name.replace(' ', '_').replace('&', '%26')})
            if not section_header:
                section_header = soup.find('span', string=re.compile(section_name, re.IGNORECASE))
            
            if section_header:
                # Trouver le tableau suivant
                table = section_header.find_parent().find_next('table')
                if table:
                    mappings.extend(parse_expansion_table(table, section_name))
        
        print(f"✅ {len(mappings)} correspondances extraites")
        return mappings
        
    except Exception as e:
        print(f"❌ Erreur scraping: {e}")
        return []

def parse_expansion_table(table, series_name):
    """Parse un tableau d'expansions"""
    
    mappings = []
    
    try:
        rows = table.find_all('tr')[1:]  # Skip header
        
        for row in rows:
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 3:
                
                # Extraire le nom de l'expansion
                name_cell = None
                for cell in cells:
                    link = cell.find('a')
                    if link and 'TCG' in link.get('href', ''):
                        name_cell = cell
                        break
                
                if name_cell:
                    link = name_cell.find('a')
                    expansion_name = link.get_text(strip=True)
                    
                    # Extraire l'année de sortie
                    release_year = None
                    for cell in cells:
                        text = cell.get_text(strip=True)
                        year_match = re.search(r'(19|20)\d{2}', text)
                        if year_match:
                            release_year = int(year_match.group())
                            break
                    
                    mapping = {
                        'bulbapedia_name': expansion_name,
                        'english_name': expansion_name,
                        'series': series_name,
                        'release_year': release_year
                    }
                    
                    mappings.append(mapping)
        
    except Exception as e:
        print(f"⚠️  Erreur parsing table: {e}")
    
    return mappings

def save_mappings_to_database(mappings):
    """Sauvegarde les correspondances en base de données"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor()
        
        print(f"💾 Sauvegarde de {len(mappings)} correspondances...")
        
        saved_count = 0
        
        for mapping in mappings:
            try:
                cursor.execute("""
                    INSERT INTO set_mappings (
                        bulbapedia_name, english_name, series, release_year
                    ) VALUES (%s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                        english_name = VALUES(english_name),
                        series = VALUES(series),
                        release_year = VALUES(release_year),
                        updated_at = CURRENT_TIMESTAMP
                """, (
                    mapping['bulbapedia_name'],
                    mapping['english_name'],
                    mapping['series'],
                    mapping['release_year']
                ))
                
                saved_count += 1
                
            except mysql.connector.Error as e:
                print(f"⚠️  Erreur sauvegarde {mapping['bulbapedia_name']}: {e}")
                continue
        
        connection.commit()
        print(f"✅ {saved_count} correspondances sauvegardées")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur sauvegarde: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def link_existing_sets():
    """Lie les sets existants avec les correspondances Bulbapedia"""
    
    try:
        connection = mysql.connector.connect(**get_db_config())
        cursor = connection.cursor(dictionary=True)
        
        print("🔗 Liaison des sets existants...")
        
        # Récupérer tous les sets existants
        cursor.execute("""
            SELECT DISTINCT e.code, e.name_en, e.name_th, e.name_id
            FROM expansions e
            WHERE e.code IS NOT NULL
        """)
        
        existing_sets = cursor.fetchall()
        
        # Récupérer les correspondances Bulbapedia
        cursor.execute("SELECT * FROM set_mappings")
        bulbapedia_mappings = cursor.fetchall()
        
        linked_count = 0
        
        for existing_set in existing_sets:
            # Chercher une correspondance
            best_match = find_best_mapping_match(existing_set, bulbapedia_mappings)
            
            if best_match:
                # Mettre à jour la correspondance avec les données existantes
                update_query = """
                    UPDATE set_mappings SET
                        thai_code = COALESCE(thai_code, %s),
                        indonesian_code = COALESCE(indonesian_code, %s),
                        thai_name = COALESCE(thai_name, %s),
                        indonesian_name = COALESCE(indonesian_name, %s),
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """
                
                thai_code = existing_set['code'] if '_TH' in existing_set['code'] else None
                indonesian_code = existing_set['code'] if '_ID' in existing_set['code'] else None
                
                cursor.execute(update_query, (
                    thai_code,
                    indonesian_code,
                    existing_set['name_th'],
                    existing_set['name_id'],
                    best_match['id']
                ))
                
                linked_count += 1
                print(f"   ✅ Lié: {existing_set['code']} → {best_match['bulbapedia_name']}")
        
        connection.commit()
        print(f"🔗 {linked_count} sets liés avec succès")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur liaison: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def find_best_mapping_match(existing_set, mappings):
    """Trouve la meilleure correspondance pour un set existant"""
    
    # Stratégies de matching
    set_code = existing_set['code']
    set_name_en = existing_set['name_en'] or ''
    
    # Nettoyer le code (enlever les suffixes régionaux)
    clean_code = re.sub(r'_(TH|ID|JP|KR|CN)$', '', set_code)
    
    for mapping in mappings:
        bulba_name = mapping['bulbapedia_name']
        english_name = mapping['english_name'] or ''
        
        # Match par nom exact
        if set_name_en and set_name_en.lower() == english_name.lower():
            return mapping
        
        # Match par nom partiel
        if set_name_en and set_name_en.lower() in english_name.lower():
            return mapping
        
        # Match par code (pour les sets récents)
        if clean_code in bulba_name or bulba_name in clean_code:
            return mapping
    
    return None

if __name__ == "__main__":
    print("🎯 SCRAPER DE CORRESPONDANCES BULBAPEDIA")
    print("=" * 50)
    
    # 1. Créer la table
    if not create_set_mappings_table():
        print("❌ Échec création table")
        sys.exit(1)
    
    # 2. Scraper les correspondances
    mappings = scrape_bulbapedia_expansions()
    if not mappings:
        print("❌ Aucune correspondance trouvée")
        sys.exit(1)
    
    # 3. Sauvegarder en base
    if not save_mappings_to_database(mappings):
        print("❌ Échec sauvegarde")
        sys.exit(1)
    
    # 4. Lier avec les sets existants
    if not link_existing_sets():
        print("❌ Échec liaison")
        sys.exit(1)
    
    print("\n🎉 CORRESPONDANCES CRÉÉES AVEC SUCCÈS!")
    print("✅ Table set_mappings créée")
    print("✅ Correspondances Bulbapedia extraites")
    print("✅ Sets existants liés")
