#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Scraper pour les cartes Pokémon Japonaises
Source: https://www.pokemon-card.com/card-search/details.php/card/{ID}/regu/{REGULATION}
"""

import requests
import mysql.connector
from bs4 import BeautifulSoup
import re
import time
import os
import logging
from urllib.parse import urljoin, urlparse
import argparse
from datetime import datetime

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraping_japanese.log'),
        logging.StreamHandler()
    ]
)

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'Munji<PERSON>',
    'password': 'Poupouille44',
    'database': 'pokemon_tcg_db',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}

BASE_URL = "https://www.pokemon-card.com"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
    'Referer': 'https://www.pokemon-card.com/',
    'Connection': 'keep-alive'
}

def get_japanese_language_id(connection):
    """Récupérer l'ID de la langue japonaise"""
    cursor = connection.cursor()
    cursor.execute("SELECT id FROM languages WHERE code = 'ja'")
    result = cursor.fetchone()
    cursor.close()
    
    if result:
        return result[0]
    else:
        # Créer la langue si elle n'existe pas
        cursor = connection.cursor()
        cursor.execute("INSERT INTO languages (code, name) VALUES ('ja', 'Japanese')")
        connection.commit()
        language_id = cursor.lastrowid
        cursor.close()
        logging.info(f"✅ Langue japonaise créée avec ID: {language_id}")
        return language_id

def generate_image_filename(card_details, regulation):
    """Générer le nom de fichier pour l'image - MÊME STRUCTURE que les autres langues"""
    card_id = card_details.get('card_id', '000')
    set_code = card_details.get('set_code', regulation)

    # Format: {SET_CODE}_JA/{REGULATION}_{ID}_JA.jpg (comme les autres langues)
    return f"{set_code}_JA/{regulation}_{card_id}_JA.jpg"

def download_image(image_url, local_path):
    """Télécharger une image depuis une URL"""
    try:
        # Créer le dossier si nécessaire
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        response = requests.get(image_url, headers=HEADERS, timeout=30)
        response.raise_for_status()
        
        with open(local_path, 'wb') as f:
            f.write(response.content)
        
        logging.info(f"Image téléchargée: {local_path}")
        return True
    except Exception as e:
        logging.error(f"Erreur téléchargement image {image_url}: {e}")
        return False

def get_card_details(card_id, regulation):
    """Récupérer les détails d'une carte depuis son ID et sa régulation"""
    detail_url = f"{BASE_URL}/card-search/details.php/card/{card_id}/regu/{regulation}"
    
    try:
        response = requests.get(detail_url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Vérifier si c'est une vraie carte (pas une page d'erreur)
        title = soup.find('title')
        if not title or 'カード検索' in title.get_text():
            return None, None  # Page de recherche = carte inexistante
        
        # Extraire les informations de la carte
        card_details = {
            'card_id': card_id,
            'regulation': regulation,
            'name': '',
            'type': '',
            'hp': '',
            'rarity': '',
            'set_code': '',
            'description': ''
        }
        
        # Nom de la carte depuis le titre
        if title:
            title_text = title.get_text(strip=True)
            if 'カード詳細（' in title_text and '）' in title_text:
                card_name = title_text.split('カード詳細（')[1].split('）')[0]
                card_details['name'] = card_name
            elif '|' in title_text:
                card_name = title_text.split('|')[0].strip()
                card_details['name'] = card_name
        
        # Type de carte
        type_element = soup.find('span', class_='type')
        if type_element:
            card_details['type'] = type_element.get_text(strip=True)
        
        # HP
        hp_element = soup.find('span', class_='hp-num')
        if hp_element:
            card_details['hp'] = hp_element.get_text(strip=True)
        
        # Chercher la vraie image de la carte et le code de set
        image_url = None

        for img in soup.find_all('img'):
            src = img.get('src', '')
            alt = img.get('alt', '')

            # Chercher l'image principale de la carte
            if '/card_images/large/' in src:
                image_url = urljoin(BASE_URL, src)

                # Extraire le set depuis l'URL de l'image
                # Format: /card_images/large/SV11B/047537_P_TSUTAJIXYA.jpg
                path_parts = src.split('/')
                if len(path_parts) >= 2:
                    set_from_url = path_parts[-2]  # SV11B
                    if set_from_url and re.match(r'^[A-Z0-9]+$', set_from_url):
                        card_details['set_code'] = set_from_url

                break

            # Fallback: chercher le code de set dans les logos de régulation
            elif '/regulation_logo_' in src or '/card/' in src:
                if alt and re.match(r'^[A-Z0-9]+$', alt):
                    card_details['set_code'] = alt
                else:
                    filename = os.path.basename(src)
                    set_match = re.search(r'([A-Z0-9]+)\.', filename)
                    if set_match:
                        card_details['set_code'] = set_match.group(1)

        # Si pas d'image trouvée, construire une URL de fallback
        if not image_url and card_details.get('set_code'):
            # Format: /assets/images/card_images/large/{SET}/{ID:06d}_P_{NAME}.jpg
            # On ne peut pas deviner le nom, donc on utilise un placeholder
            padded_id = str(card_id).zfill(6)
            image_url = f"https://www.pokemon-card.com/assets/images/card_images/large/{card_details['set_code']}/{padded_id}_P_UNKNOWN.jpg"

        # Description (texte de saveur)
        card_div = soup.find('div', class_='card')
        if card_div:
            description_text = card_div.get_text(strip=True)
            if description_text and len(description_text) < 500:
                card_details['description'] = description_text
        
        return card_details, image_url
        
    except Exception as e:
        logging.error(f"Erreur récupération détails carte {card_id}/{regulation}: {e}")
        return None, None

def get_expansion_id(connection, set_code, regulation):
    """Récupérer l'ID de l'expansion depuis son code"""
    if not set_code:
        set_code = f"{regulation}_UNKNOWN"
    
    cursor = connection.cursor()
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    result = cursor.fetchone()
    cursor.close()
    
    if result:
        return result[0]
    else:
        # Créer l'expansion si elle n'existe pas
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO expansions (code, name_en) 
            VALUES (%s, %s)
        """, (set_code, set_code))
        connection.commit()
        expansion_id = cursor.lastrowid
        cursor.close()
        logging.info(f"✅ Expansion {set_code} créée avec ID: {expansion_id}")
        return expansion_id

def card_exists(connection, card_id, regulation, japanese_language_id):
    """Vérifier si une carte existe déjà"""
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id FROM card_versions 
        WHERE web_id = %s AND language_id = %s AND source = 'japanese'
    """, (f"{regulation}_{card_id}", japanese_language_id))
    result = cursor.fetchone()
    cursor.close()
    return result is not None

def create_card_post(connection, card_details, force_update=False):
    """Créer ou mettre à jour une carte dans la base de données"""
    japanese_language_id = get_japanese_language_id(connection)
    card_id = card_details['card_id']
    regulation = card_details['regulation']
    web_id = f"{regulation}_{card_id}"
    
    # Vérifier si la carte existe déjà
    if card_exists(connection, card_id, regulation, japanese_language_id) and not force_update:
        print(f"   🔍 Carte {web_id} existe déjà - vérification des données manquantes...")
        
        # Mettre à jour le chemin local ET l'URL de l'image
        image_filename = generate_image_filename(card_details, regulation)
        local_image_path = f"images/{image_filename}"
        image_url = card_details.get('image_url', '')

        cursor = connection.cursor()
        cursor.execute("""
            UPDATE card_versions
            SET local_image_path = %s, image_url = %s
            WHERE web_id = %s AND language_id = %s
        """, (local_image_path, image_url, web_id, japanese_language_id))
        connection.commit()
        cursor.close()
        print(f"   ✅ Chemin local et URL mis à jour: {local_image_path}")

        # Télécharger l'image si elle n'existe pas
        if image_url:
            full_local_path = f"../images/{image_filename}"
            if not os.path.exists(full_local_path):
                if download_image(image_url, full_local_path):
                    print(f"   ✅ Image téléchargée: {os.path.basename(image_filename)}")
                else:
                    print(f"   ❌ Échec téléchargement image")
            else:
                print(f"   ✅ Image déjà présente: {os.path.basename(image_filename)}")
        
        return None  # Carte déjà complète

    # Récupérer l'expansion
    expansion_id = get_expansion_id(connection, card_details.get('set_code'), regulation)
    
    # Créer la carte maître si elle n'existe pas
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id FROM pokemon_cards 
        WHERE card_number = %s AND expansion_id = %s
    """, (str(card_id), expansion_id))
    
    existing_card = cursor.fetchone()
    
    if existing_card:
        card_db_id = existing_card[0]
        print(f"   ✅ Carte maître existante trouvée avec ID: {card_db_id}")
    else:
        # Créer une nouvelle carte maître
        cursor.execute("""
            INSERT INTO pokemon_cards (card_number, expansion_id, source)
            VALUES (%s, %s, %s)
        """, (
            str(card_id),
            expansion_id,
            'japanese'
        ))
        connection.commit()
        card_db_id = cursor.lastrowid
        print(f"   ✅ Carte maître créée avec ID: {card_db_id}")
    
    # Générer le chemin local de l'image
    image_filename = generate_image_filename(card_details, regulation)
    local_image_path = f"images/{image_filename}"
    
    # Créer la version japonaise
    cursor.execute("""
        INSERT INTO card_versions (card_id, language_id, name, rarity, web_id, source, image_url, local_image_path)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        card_db_id,
        japanese_language_id,
        card_details.get('name', ''),
        card_details.get('rarity', ''),
        web_id,
        'japanese',
        card_details.get('image_url', ''),
        local_image_path
    ))
    connection.commit()
    
    print(f"   💾 {card_details.get('name', 'Carte sans nom')} - Sauvegardée")
    
    cursor.close()
    logging.info(f"Carte ajoutée/mise à jour avec succès (ID DB: {card_db_id}, Web ID: {web_id})")
    return card_db_id

def scrape_regulation_cards(connection, regulation, start_id=1, end_id=50000):
    """Scraper toutes les cartes d'une régulation"""
    print(f"\n📦 RÉGULATION {regulation}")
    print(f"\n🎯 SCRAPING RÉGULATION: {regulation}")
    print(f"🔢 Plage d'IDs: {start_id} à {end_id}")
    print("--------------------------------------------------")

    total_saved = 0
    consecutive_failures = 0
    max_consecutive_failures = 100  # Arrêter après 100 échecs consécutifs

    for card_id in range(start_id, end_id + 1):
        try:
            print(f"      🔍 Récupération carte {regulation}/{card_id}...")

            # Récupérer les détails de la carte
            card_details, image_url = get_card_details(card_id, regulation)

            if card_details:
                card_details['image_url'] = image_url

                # Sauvegarder la carte
                card_db_id = create_card_post(connection, card_details)

                if card_db_id:
                    total_saved += 1
                    print(f"      ✅ Carte sauvegardée")
                else:
                    print(f"      ⚠️  Carte {regulation}/{card_id} - Déjà existante")

                consecutive_failures = 0  # Reset le compteur d'échecs

                # Télécharger l'image si elle n'existe pas
                image_filename = generate_image_filename(card_details, regulation)
                local_image_path = f"../images/{image_filename}"

                if not os.path.exists(local_image_path) and image_url:
                    if download_image(image_url, local_image_path):
                        print(f"      ✅ Image téléchargée: {os.path.basename(image_filename)}")
                    else:
                        print(f"      ⚠️  Échec téléchargement image")

            else:
                consecutive_failures += 1
                print(f"      ❌ Carte {regulation}/{card_id} inexistante ({consecutive_failures} échecs consécutifs)")

                # Si trop d'échecs consécutifs, on peut supposer qu'on a atteint la fin
                if consecutive_failures >= max_consecutive_failures:
                    print(f"      🛑 {max_consecutive_failures} échecs consécutifs - fin probable de la régulation {regulation}")
                    break

            # Pause pour éviter de surcharger le serveur
            time.sleep(0.3)

            # Affichage du progrès tous les 100 cartes
            if card_id % 100 == 0:
                print(f"   📊 Progrès: {card_id}/{end_id} cartes vérifiées, {total_saved} sauvegardées")

        except Exception as e:
            logging.error(f"Erreur traitement carte {regulation}/{card_id}: {e}")
            consecutive_failures += 1
            continue

    print(f"✅ RÉGULATION {regulation} TERMINÉE: {total_saved} cartes sauvegardées")
    return total_saved

def scrape_all_japanese_cards_progressive(connection):
    """Scraper progressivement toutes les cartes japonaises"""
    print("🎯 SCRAPING PROGRESSIF DE TOUTES LES CARTES JAPONAISES")
    print("============================================================")

    # Régulations à scraper (du plus récent au plus ancien)
    regulations = ['SV', 'SM', 'XY', 'BW', 'DP']

    total_saved = 0

    for i, regulation in enumerate(regulations, 1):
        print(f"\n📦 RÉGULATION {i}/{len(regulations)}: {regulation}")

        try:
            # Ajuster les plages selon la régulation
            if regulation == 'SV':
                start_id, end_id = 1, 30000  # Régulation actuelle
            elif regulation == 'SM':
                start_id, end_id = 1, 25000
            elif regulation == 'XY':
                start_id, end_id = 1, 20000
            else:
                start_id, end_id = 1, 15000  # Régulations plus anciennes

            saved_count = scrape_regulation_cards(connection, regulation, start_id, end_id)
            total_saved += saved_count

            print(f"✅ RÉGULATION {regulation}: {saved_count} cartes sauvegardées")

        except Exception as e:
            logging.error(f"Erreur scraping régulation {regulation}: {e}")
            continue

    print(f"\n🎉 SCRAPING TERMINÉ: {total_saved} cartes japonaises sauvegardées au total")
    return total_saved

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description='Scraper Japanese Pokemon TCG')
    parser.add_argument('--regulation', help='Régulation à scraper (SV, SM, XY, etc.)')
    parser.add_argument('--start-id', type=int, default=1, help='ID de début')
    parser.add_argument('--end-id', type=int, default=50000, help='ID de fin')
    parser.add_argument('--force-update', action='store_true', help='Forcer la mise à jour des cartes existantes')
    args = parser.parse_args()

    print("🔥 SCRAPING COMPLET - TOUTES LES RÉGULATIONS ET TOUTES LES CARTES")
    print("🇯🇵 SCRAPER JAPONAIS POKEMON TCG")
    print("============================================================")
    print("🎯 Objectif: Récupérer TOUTES les cartes depuis le site japonais officiel")
    print("📊 Source: www.pokemon-card.com/card-search/details.php/")
    print("============================================================")

    # Connexion à la base de données
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        print("✅ Connexion à la base de données établie")

        if args.regulation:
            # Scraper une régulation spécifique
            saved_count = scrape_regulation_cards(connection, args.regulation, args.start_id, args.end_id)
        else:
            # Scraper toutes les régulations
            saved_count = scrape_all_japanese_cards_progressive(connection)

        connection.close()
        print(f"\n🎉 SCRAPING TERMINÉ: {saved_count} cartes sauvegardées")

    except mysql.connector.Error as e:
        print(f"❌ Erreur de base de données: {e}")
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        logging.error(f"Erreur durant le scraping: {e}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            connection.close()
            print("Connexion à la base de données fermée")

if __name__ == "__main__":
    main()
