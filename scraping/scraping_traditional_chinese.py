#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Scraper pour les cartes Pokémon Traditional Chinese (Hong Kong)
Source: https://asia.pokemon-card.com/hk/
"""

import requests
import mysql.connector
from bs4 import BeautifulSoup
import re
import time
import os
import logging
from urllib.parse import urljoin, urlparse
import argparse
from datetime import datetime

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraping_traditional_chinese.log'),
        logging.StreamHandler()
    ]
)

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': '<PERSON><PERSON><PERSON>',
    'password': 'Poupouille44',
    'database': 'pokemon_tcg_db',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}

BASE_URL = "https://asia.pokemon-card.com"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def get_traditional_chinese_language_id(connection):
    """Récupérer l'ID de la langue Traditional Chinese"""
    cursor = connection.cursor()
    cursor.execute("SELECT id FROM languages WHERE code = 'zh-tw'")
    result = cursor.fetchone()
    cursor.close()
    
    if result:
        return result[0]
    else:
        # Créer la langue si elle n'existe pas
        cursor = connection.cursor()
        cursor.execute("INSERT INTO languages (code, name) VALUES ('zh-tw', 'Traditional Chinese (Hong Kong)')")
        connection.commit()
        language_id = cursor.lastrowid
        cursor.close()
        logging.info(f"✅ Langue Traditional Chinese créée avec ID: {language_id}")
        return language_id

def generate_image_filename(card_details, set_code):
    """Générer le nom de fichier pour l'image"""
    card_number = card_details.get('card_number', '000')
    
    # Vérifier que card_number n'est pas None
    if card_number is None:
        card_number = '000'

    # Nettoyer le numéro de carte (prendre seulement la partie avant le /)
    if '/' in card_number:
        clean_number = card_number.split('/')[0].zfill(3)
        total_cards = card_number.split('/')[1].zfill(3)
    else:
        clean_number = str(card_number).zfill(3)
        total_cards = "999"  # Valeur par défaut

    return f"{clean_number}_{total_cards}.jpg"

def download_image(image_url, local_path):
    """Télécharger une image depuis une URL"""
    try:
        # Créer le dossier si nécessaire
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        response = requests.get(image_url, headers=HEADERS, timeout=30)
        response.raise_for_status()
        
        with open(local_path, 'wb') as f:
            f.write(response.content)
        
        logging.info(f"Image téléchargée: {local_path}")
        return True
    except Exception as e:
        logging.error(f"Erreur téléchargement image {image_url}: {e}")
        return False

def get_card_details(card_id):
    """Récupérer les détails d'une carte depuis son ID"""
    detail_url = f"{BASE_URL}/hk/card-search/detail/{card_id}/"
    
    try:
        response = requests.get(detail_url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extraire les informations de la carte
        card_details = {
            'web_id': card_id,
            'name': '',
            'card_number': '',
            'rarity': '',
            'type': '',
            'hp': '',
            'artist': '',
            'description': ''
        }
        
        # Nom de la carte
        name_element = soup.find('h1', class_='cardName')
        if name_element:
            card_details['name'] = name_element.get_text(strip=True)
        
        # Numéro de carte
        number_element = soup.find('span', class_='cardNumber')
        if number_element:
            card_details['card_number'] = number_element.get_text(strip=True)
        
        # Rareté
        rarity_element = soup.find('span', class_='rarity')
        if rarity_element:
            card_details['rarity'] = rarity_element.get_text(strip=True)
        
        # Construire l'URL de l'image directement à partir de l'ID
        # Format: https://asia.pokemon-card.com/hk/card-img/hk{ID:08d}.png
        image_url = f"https://asia.pokemon-card.com/hk/card-img/hk{card_id:08d}.png"
        
        return card_details, image_url
        
    except Exception as e:
        logging.error(f"Erreur récupération détails carte {card_id}: {e}")
        return None, None

def get_expansion_id(connection, set_code):
    """Récupérer l'ID de l'expansion depuis son code"""
    cursor = connection.cursor()
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    result = cursor.fetchone()
    cursor.close()
    
    if result:
        return result[0]
    else:
        # Créer l'expansion si elle n'existe pas
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO expansions (code, name_en, name_zh_tw) 
            VALUES (%s, %s, %s)
        """, (set_code, set_code, set_code))
        connection.commit()
        expansion_id = cursor.lastrowid
        cursor.close()
        logging.info(f"✅ Expansion {set_code} créée avec ID: {expansion_id}")
        return expansion_id

def card_exists(connection, web_id, traditional_chinese_language_id):
    """Vérifier si une carte existe déjà"""
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id FROM card_versions 
        WHERE web_id = %s AND language_id = %s
    """, (str(web_id), traditional_chinese_language_id))
    result = cursor.fetchone()
    cursor.close()
    return result is not None

def create_card_post(connection, card_details, set_code, force_update=False):
    """Créer ou mettre à jour une carte dans la base de données"""
    traditional_chinese_language_id = get_traditional_chinese_language_id(connection)
    web_id = card_details['web_id']
    
    # Vérifier si la carte existe déjà
    if card_exists(connection, web_id, traditional_chinese_language_id) and not force_update:
        print(f"   🔍 Carte {web_id} existe déjà - vérification des données manquantes...")
        
        # Vérifier si l'image manque et la télécharger si nécessaire
        if card_details.get('imageUrl'):
            image_filename = generate_image_filename(card_details, set_code)
            local_image_path = f"../images/{set_code}_ZH_TW/{image_filename}"

            # Vérifier si l'image existe déjà localement
            if not os.path.exists(local_image_path):
                print(f"   📸 Image manquante - téléchargement...")
                if download_image(card_details['imageUrl'], local_image_path):
                    print(f"   ✅ Image Traditional Chinese téléchargée: {local_image_path}")
                else:
                    print(f"   ❌ Échec téléchargement image: {card_details['imageUrl']}")
            else:
                print(f"   ✅ Image déjà présente: {local_image_path}")
            
            # IMPORTANT: Mettre à jour l'image_url en base de données
            cursor = connection.cursor()
            cursor.execute("""
                UPDATE card_versions 
                SET image_url = %s 
                WHERE web_id = %s AND language_id = %s
            """, (card_details.get('imageUrl', ''), str(web_id), traditional_chinese_language_id))
            connection.commit()
            print(f"   ✅ URL d'image mise à jour en base: {card_details.get('imageUrl', '')}")

        return None  # Carte déjà complète

    # Récupérer l'expansion
    expansion_id = get_expansion_id(connection, set_code)
    
    # Créer la carte maître si elle n'existe pas
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id FROM pokemon_cards 
        WHERE card_number = %s AND expansion_id = %s
    """, (card_details.get('card_number', ''), expansion_id))
    
    existing_card = cursor.fetchone()
    
    if existing_card:
        card_id = existing_card[0]
        print(f"   ✅ Carte maître existante trouvée avec ID: {card_id}")
    else:
        # Créer une nouvelle carte maître
        cursor.execute("""
            INSERT INTO pokemon_cards (card_number, expansion_id, source)
            VALUES (%s, %s, %s)
        """, (
            card_details.get('card_number', ''),
            expansion_id,
            'traditional_chinese'
        ))
        connection.commit()
        card_id = cursor.lastrowid
        print(f"   ✅ Carte maître créée avec ID: {card_id}")
    
    # Créer la version Traditional Chinese
    cursor.execute("""
        INSERT INTO card_versions (card_id, language_id, name, rarity, web_id, source, image_url)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
    """, (
        card_id,
        traditional_chinese_language_id,
        card_details.get('name', ''),
        card_details.get('rarity', ''),
        str(web_id),
        'traditional_chinese',
        card_details.get('imageUrl', '')
    ))
    connection.commit()
    
    print(f"   💾 {card_details.get('name', 'Carte sans nom')} - Sauvegardée")
    
    # Télécharger l'image
    if card_details.get('imageUrl'):
        image_filename = generate_image_filename(card_details, set_code)
        local_image_path = f"../images/{set_code}_ZH_TW/{image_filename}"
        
        if download_image(card_details['imageUrl'], local_image_path):
            print(f"   ✅ Image Traditional Chinese téléchargée: {local_image_path}")
        else:
            print(f"   ❌ Échec téléchargement image: {card_details['imageUrl']}")
    
    cursor.close()
    logging.info(f"Carte ajoutée/mise à jour avec succès (ID DB: {card_id}, Web ID: {web_id})")
    return card_id

def scrape_expansion_cards(connection, expansion_code):
    """Scraper toutes les cartes d'une expansion"""
    print(f"\n📦 SET {expansion_code}")
    print(f"\n🎯 SCRAPING SET: {expansion_code}")
    print("--------------------------------------------------")

    page = 1
    total_saved = 0

    while True:
        # URL de la page de recherche (même format que le thaï)
        search_url = f"{BASE_URL}/hk/card-search/list/?expansionCodes={expansion_code}&pageNo={page}"

        try:
            response = requests.get(search_url, headers=HEADERS, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Trouver toutes les cartes sur cette page
            card_elements = soup.find_all('div', class_='cardItem')

            if not card_elements:
                print(f"   📄 Page {page}: Aucune carte trouvée - fin du set")
                break

            print(f"   📄 Page {page}: {len(card_elements)} cartes trouvées")

            for card_element in card_elements:
                try:
                    # Extraire l'ID de la carte depuis le lien
                    link_element = card_element.find('a')
                    if not link_element:
                        continue

                    href = link_element.get('href', '')
                    card_id_match = re.search(r'/detail/(\d+)/', href)
                    if not card_id_match:
                        continue

                    card_id = int(card_id_match.group(1))
                    print(f"      🔍 Récupération détails carte {card_id}...")

                    # Récupérer les détails de la carte
                    card_details, image_url = get_card_details(card_id)

                    if card_details:
                        card_details['imageUrl'] = image_url

                        # Sauvegarder la carte
                        card_db_id = create_card_post(connection, card_details, expansion_code)

                        if card_db_id:
                            total_saved += 1
                            print(f"      ✅ Carte sauvegardée")
                        else:
                            print(f"      ⚠️  Card {card_id} - Déjà existante ou erreur")
                    else:
                        print(f"      ❌ Impossible de récupérer les détails de la carte {card_id}")

                    # Pause pour éviter de surcharger le serveur
                    time.sleep(0.5)

                except Exception as e:
                    logging.error(f"Erreur traitement carte: {e}")
                    continue

            page += 1
            time.sleep(1)  # Pause entre les pages

        except Exception as e:
            logging.error(f"Erreur scraping page {page}: {e}")
            break

    print(f"✅ SET {expansion_code} TERMINÉ: {total_saved} cartes sauvegardées")
    return total_saved

def get_all_traditional_chinese_sets():
    """Récupérer tous les sets Traditional Chinese disponibles depuis la page produits"""
    print("🔍 RÉCUPÉRATION DE TOUS LES SETS DISPONIBLES...")

    try:
        print("🔍 Tentative 1: Page produits /hk/products/")
        response = requests.get(f"{BASE_URL}/hk/products/", headers=HEADERS, timeout=20)
        print(f"Status: {response.status_code}")

        if response.status_code != 200:
            print(f"❌ Erreur HTTP {response.status_code} sur /hk/products/")
            raise Exception(f"HTTP {response.status_code}")

        soup = BeautifulSoup(response.content, 'html.parser')

        sets = []
        seen_codes = set()

        print("🔍 Recherche de liens produits...")
        # Chercher TOUS les liens, pas seulement ceux avec /hk/products/
        all_links = soup.find_all('a', href=True)
        print(f"Total liens trouvés: {len(all_links)}")

        for link in all_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)

            # Chercher des patterns de sets dans les URLs
            set_patterns = re.findall(r'\b(SV\d+[a-zA-Z]*|S\d+[a-zA-Z]*)\b', href)
            for code in set_patterns:
                if code not in seen_codes and len(code) <= 10:
                    seen_codes.add(code)
                    sets.append({
                        'code': code,
                        'name': text if text else f'Set {code}'
                    })
                    print(f"   ✅ Set trouvé dans URL: {code}")

        # Méthode 2: Analyser tout le contenu texte de la page
        print("🔍 Analyse du contenu texte...")
        page_text = soup.get_text()
        set_patterns = re.findall(r'\b(SV\d+[a-zA-Z]*|S\d+[a-zA-Z]*)\b', page_text)

        for code in set(set_patterns):
            if code not in seen_codes and len(code) <= 10:
                seen_codes.add(code)
                sets.append({
                    'code': code,
                    'name': f'Set {code}'
                })
                print(f"   ✅ Set trouvé dans texte: {code}")

        # Méthode 3: Si toujours rien, essayer la page de recherche
        if not sets:
            print("🔍 Tentative 2: Page de recherche /hk/card-search/list/")
            search_response = requests.get(f"{BASE_URL}/hk/card-search/list/", headers=HEADERS, timeout=20)
            if search_response.status_code == 200:
                search_soup = BeautifulSoup(search_response.content, 'html.parser')

                # Chercher un sélecteur d'expansions
                selects = search_soup.find_all('select')
                for select in selects:
                    options = select.find_all('option')
                    for option in options:
                        value = option.get('value', '').strip()
                        text = option.get_text(strip=True)

                        if value and re.match(r'^[A-Z0-9\-]+$', value) and len(value) <= 10:
                            if value not in seen_codes:
                                seen_codes.add(value)
                                sets.append({
                                    'code': value,
                                    'name': text if text else f'Set {value}'
                                })
                                print(f"   ✅ Set trouvé dans sélecteur: {value}")

        # Si aucun set détecté, c'est un ÉCHEC - pas de fallback stupide !
        if not sets:
            print("❌ AUCUN SET DÉTECTÉ AUTOMATIQUEMENT")
            print("❌ Le scraper doit détecter les vrais sets depuis le site, pas utiliser une liste hardcodée !")
            print("❌ Vérifiez la connectivité ou la structure HTML du site")
            return []

        print("📦 SETS TROUVÉS:")
        for set_info in sets:
            print(f"   ✅ {set_info['code']}: {set_info['name']}")

        print(f"\n📊 TOTAL: {len(sets)} sets trouvés")
        return sets

    except Exception as e:
        logging.error(f"Erreur récupération des sets: {e}")
        print("❌ ERREUR lors de la récupération des sets")
        print("❌ Pas de fallback - le scraper doit fonctionner avec les vrais sets du site !")
        return []

def scrape_all_traditional_chinese_cards_progressive(connection):
    """Scraper progressivement toutes les cartes Traditional Chinese"""
    print("🎯 SCRAPING PROGRESSIF DE TOUTES LES CARTES TRADITIONAL CHINESE")
    print("============================================================")

    # Récupérer tous les sets
    all_sets = get_all_traditional_chinese_sets()

    if not all_sets:
        print("❌ Aucun set trouvé")
        return 0

    total_saved = 0

    for i, set_info in enumerate(all_sets, 1):
        expansion_code = set_info['code']
        print(f"\n📦 SET {i}/{len(all_sets)}: {expansion_code}")

        try:
            saved_count = scrape_expansion_cards(connection, expansion_code)
            total_saved += saved_count

            print(f"✅ SET {expansion_code}: {saved_count} cartes sauvegardées")

        except Exception as e:
            logging.error(f"Erreur scraping set {expansion_code}: {e}")
            continue

    print(f"\n🎉 SCRAPING TERMINÉ: {total_saved} cartes Traditional Chinese sauvegardées au total")
    return total_saved

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description='Scraper Traditional Chinese Pokemon TCG')
    parser.add_argument('--set', help='Code du set à scraper (optionnel)')
    parser.add_argument('--force-update', action='store_true', help='Forcer la mise à jour des cartes existantes')
    args = parser.parse_args()

    print("🔥 SCRAPING COMPLET - TOUS LES SETS ET TOUTES LES CARTES")
    print("🇭🇰 SCRAPER TRADITIONAL CHINESE POKEMON TCG")
    print("============================================================")
    print("🎯 Objectif: Récupérer TOUTES les cartes depuis le site Traditional Chinese")
    print("📊 Source: asia.pokemon-card.com/hk/")
    print("============================================================")

    # Connexion à la base de données
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        print("✅ Connexion à la base de données établie")

        if args.set:
            # Scraper un set spécifique
            saved_count = scrape_expansion_cards(connection, args.set)
        else:
            # Scraper tous les sets
            saved_count = scrape_all_traditional_chinese_cards_progressive(connection)

        connection.close()
        print(f"\n🎉 SCRAPING TERMINÉ: {saved_count} cartes sauvegardées")

    except mysql.connector.Error as e:
        print(f"❌ Erreur de base de données: {e}")
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        logging.error(f"Erreur durant le scraping: {e}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            connection.close()
            print("Connexion à la base de données fermée")

if __name__ == "__main__":
    main()
