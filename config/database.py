#!/usr/bin/env python3
"""
Centralized database configuration for Pokemon TCG Scraper
"""

import os
from typing import Dict, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class DatabaseConfig:
    """Centralized database configuration class"""
    
    def __init__(self):
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, any]:
        """Load database configuration from environment variables with fallbacks"""
        return {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', '3306')),  # Default to MariaDB standard port
            'user': os.getenv('DB_USER', 'pokemon_user'),
            'password': os.getenv('DB_PASSWORD', 'pokemon_pass'),
            'database': os.getenv('DB_DATABASE', 'pokemon_tcg_db'),
            'unix_socket': os.getenv('DB_UNIX_SOCKET', ''),  # Empty for MariaDB
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci',
            'autocommit': False,
            'raise_on_warnings': True
        }
    
    @property
    def config(self) -> Dict[str, any]:
        """Get the database configuration dictionary"""
        return self._config.copy()
    
    @property
    def mysql_connector_config(self) -> Dict[str, any]:
        """Get configuration formatted for mysql.connector.connect()"""
        config = self._config.copy()
        
        # Remove unix_socket if it's empty or None
        if not config.get('unix_socket'):
            config.pop('unix_socket', None)
        
        # Remove custom fields that mysql.connector doesn't recognize
        config.pop('collation', None)
        
        return config
    
    @property
    def mcp_config(self) -> Dict[str, str]:
        """Get configuration formatted for MCP environment variables"""
        return {
            'MYSQL_HOST': str(self._config['host']),
            'MYSQL_PORT': str(self._config['port']),
            'MYSQL_USER': self._config['user'],
            'MYSQL_PASSWORD': self._config['password'],
            'MYSQL_DATABASE': self._config['database'],
            'MYSQL_UNIX_SOCKET': self._config.get('unix_socket', '')
        }
    
    def get_connection_string(self) -> str:
        """Get a connection string for logging/debugging purposes"""
        return f"mysql://{self._config['user']}@{self._config['host']}:{self._config['port']}/{self._config['database']}"
    
    def validate_config(self) -> bool:
        """Validate that required configuration is present"""
        required_fields = ['host', 'port', 'user', 'password', 'database']
        for field in required_fields:
            if not self._config.get(field):
                raise ValueError(f"Database configuration missing required field: {field}")
        return True

# Global instance
db_config = DatabaseConfig()

# Convenience functions for backward compatibility
def get_db_config() -> Dict[str, any]:
    """Get database configuration dictionary (mysql.connector format)"""
    return db_config.mysql_connector_config

def get_mcp_config() -> Dict[str, str]:
    """Get MCP environment configuration"""
    return db_config.mcp_config

def get_connection_string() -> str:
    """Get connection string for logging"""
    return db_config.get_connection_string()

# Legacy aliases for existing code
DB_CONFIG = get_db_config()
wp_config = get_db_config()  # For files that use wp_config
