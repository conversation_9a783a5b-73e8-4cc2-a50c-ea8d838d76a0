#!/usr/bin/env python3
"""
Pokemon TCG Scraper - Web Interface Launcher
Simple launcher like on Mac - no virtual environment issues
"""

import os
import sys
import subprocess

def main():
    print("🎯 Pokemon TCG Scraper - Web Interface Launcher")
    print("=" * 60)
    
    # Project paths
    project_root = "/media/devmon/piHDD/Sites/pokemon-tcg-scraper"
    scraping_dir = os.path.join(project_root, "scraping")
    
    print(f"📂 Project root: {project_root}")
    print(f"📂 Scraping dir: {scraping_dir}")
    
    # Check if directories exist
    if not os.path.exists(project_root):
        print(f"❌ Project directory not found: {project_root}")
        return
        
    if not os.path.exists(scraping_dir):
        print(f"❌ Scraping directory not found: {scraping_dir}")
        return
    
    # Check if app.py exists
    app_file = os.path.join(scraping_dir, "app.py")
    if not os.path.exists(app_file):
        print(f"❌ App file not found: {app_file}")
        return
    
    print("✅ All files found")
    print()
    
    # Prepare environment
    env = os.environ.copy()
    env['PYTHONPATH'] = project_root
    
    # Remove virtual environment if present
    if 'VIRTUAL_ENV' in env:
        del env['VIRTUAL_ENV']
        print("🔧 Virtual environment disabled")
    
    print("🚀 Starting Flask application...")
    print("📍 Local URL: http://127.0.0.1:5051/")
    print("🌐 Network URL: http://*************:5051/")
    print("🔧 Debug mode: ON")
    print("⚡ Like on your Mac!")
    print("=" * 60)
    print()
    print("💡 Press Ctrl+C to stop the server")
    print()

    try:
        # Set Flask to listen on all interfaces
        env['FLASK_HOST'] = '0.0.0.0'
        env['FLASK_PORT'] = '5051'

        # Launch the Flask application
        subprocess.run([
            'python3', 'app.py'
        ], cwd=scraping_dir, env=env)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
